import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "育儿手册 - 新手父母的贴心指南",
  description: "专为新手父母打造的育儿指南，涵盖母乳喂养、配方奶、辅食、睡眠、过敏等各个方面的实用知识和专业建议",
  keywords: "育儿, 新手父母, 母乳喂养, 配方奶, 辅食, 宝宝睡眠, 过敏, 婴儿护理",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link 
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" 
          rel="stylesheet"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
