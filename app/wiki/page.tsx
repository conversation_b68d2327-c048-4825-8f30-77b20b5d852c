"use client"

import Link from 'next/link';

// L0 Categories with L1 subcategories
const categories = [
  {
    id: 'feeding',
    title: '喂食',
    icon: '🍼',
    chapters: [
      {
        id: 'mother-milk',
        title: '母乳喂养',
        icon: '🤱',
        sections: [
          { id: 'feeding_basics', title: '母乳喂养' },
          { id: 'pumping_storage', title: '挤奶与存储' },
          { id: 'other_problems', title: '其它问题' }
        ],
        color: 'bg-blue-50 border-blue-200 hover:border-blue-400',
        textColor: 'text-blue-700',
        borderColor: 'border-blue-200',
        hoverBgColor: 'hover:bg-blue-400'
      },
      {
        id: 'milk-powder',
        title: '配方奶',
        icon: '🍼',
        sections: [
          { id: 'feeding_method', title: '喂养方法' },
          { id: 'formula_choice', title: '奶粉选择' },
          { id: 'other_problems', title: '其它问题' }
        ],
        color: 'bg-orange-50 border-orange-200 hover:border-orange-400',
        textColor: 'text-orange-700',
        borderColor: 'border-orange-200',
        hoverBgColor: 'hover:bg-orange-400'
      },
      {
        id: 'side-food',
        title: '辅食',
        icon: '🥣',
        sections: [
          { id: 'tab1', title: '辅食制作' },
          { id: 'tab2', title: '辅食种类' },
          { id: 'tab3', title: '食谱推荐' },
          { id: 'tab4', title: '其它问题' }
        ],
        color: 'bg-green-50 border-green-200 hover:border-green-400',
        textColor: 'text-green-700',
        borderColor: 'border-green-200',
        hoverBgColor: 'hover:bg-green-400'
      },
      {
        id: 'baby-allergies',
        title: '过敏和食欲',
        icon: '🤧',
        sections: [
          { id: 'baby_allergy', title: '宝宝过敏' },
          { id: 'appetite_improvement', title: '食欲提升' },
          { id: 'other_problems', title: '常见问题' }
        ],
        color: 'bg-red-50 border-red-200 hover:border-red-400',
        textColor: 'text-red-700',
        borderColor: 'border-red-200',
        hoverBgColor: 'hover:bg-red-400'
      }
    ]
  },
  {
    id: 'sleep',
    title: '睡眠',
    icon: '😴',
    chapters: [
      {
        id: 'baby-sleep',
        title: '宝宝睡眠指南',
        icon: '😴',
        sections: [
          { id: 'sleep_methods', title: '哄睡方法' },
          { id: 'sleep_routine', title: '睡眠规律' },
          { id: 'safe_sleep', title: '安全睡眠' },
          { id: 'sleep_development', title: '睡眠发育' }
        ],
        color: 'bg-purple-50 border-purple-200 hover:border-purple-400',
        textColor: 'text-purple-700',
        borderColor: 'border-purple-200',
        hoverBgColor: 'hover:bg-purple-400'
      }
    ]
  },
  {
    id: 'health',
    title: '健康护理',
    icon: '🩺',
    chapters: [
      {
        id: 'baby-wastes',
        title: '便便指南',
        icon: '💩',
        sections: [
          { id: 'diaper_change', title: '尿布更换' },
          { id: 'poop_colors', title: '颜色解读' },
          { id: 'frequency_problems', title: '频率问题' }
        ],
        color: 'bg-yellow-50 border-yellow-200 hover:border-yellow-400',
        textColor: 'text-yellow-700',
        borderColor: 'border-yellow-200',
        hoverBgColor: 'hover:bg-yellow-400'
      },
      {
        id: 'baby-health',
        title: '健康安全指南',
        icon: '🩺',
        sections: [
          { id: 'health_checks', title: '健康检查' },
          { id: 'common_illnesses', title: '常见疾病' },
          { id: 'first_aid', title: '紧急急救' }
        ],
        color: 'bg-pink-50 border-pink-200 hover:border-pink-400',
        textColor: 'text-pink-700',
        borderColor: 'border-pink-200',
        hoverBgColor: 'hover:bg-pink-400'
      }
    ]
  }
];

export default function WikiPage() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#FFFBF5', color: '#434242', fontFamily: "'Noto Sans SC', sans-serif" }}>
      {/* Compact Header */}
      <header className="text-center py-6 px-4" style={{ backgroundColor: '#A57C4F' }}>
        <h1 className="text-4xl font-bold text-white mb-2">育儿手册</h1>
        <p className="text-base text-white/90">新手父母的贴心指南</p>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-6">
        {/* Categories */}
        {categories.map((category) => (
          <section key={category.id} className="mb-8">
            {/* L0 Category Header */}
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">{category.icon}</span>
              <h2 className="text-2xl font-bold" style={{ color: '#A57C4F' }}>
                {category.title}
              </h2>
            </div>

            {/* L1 Chapters Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {category.chapters.map((chapter) => (
                <div 
                  key={chapter.id} 
                  className={`p-4 rounded-lg border transition-all duration-300 hover:shadow-md ${chapter.color}`}
                >
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-lg">{chapter.icon}</span>
                    <h3 className={`text-lg font-semibold ${chapter.textColor}`}>
                      {chapter.title}
                    </h3>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-1">
                    {chapter.sections.map((section) => (
                      <Link
                        key={section.id}
                        href={`/wiki/${chapter.id}#${section.id}`}
                        className={`text-base px-2 py-1 rounded text-center transition-all duration-200 hover:scale-105 bg-white border text-black ${chapter.borderColor} ${chapter.hoverBgColor} hover:text-white`}
                      >
                        {section.title}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        ))}
      </main>

      {/* Compact Footer */}
      <footer className="text-center py-4 border-t border-gray-200">
        <p className="text-gray-500 text-sm">
          本手册信息仅供参考，如有健康问题请咨询专业医生
        </p>
      </footer>
    </div>
  );
}