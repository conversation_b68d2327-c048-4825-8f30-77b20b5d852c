import { NextRequest, NextResponse } from 'next/server';
import { createReadStream } from 'fs';
import { stat } from 'fs/promises';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string[] }> }
) {
  try {
    // Await params before accessing properties
    const { slug } = await params;
    // Join the slug array to create the filename
    const filename = slug.join('-') + '.html';
    const filePath = path.join(process.cwd(), 'htmls', filename);

    // Check if file exists
    try {
      await stat(filePath);
    } catch (error) {
      return new NextResponse('HTML file not found', { status: 404 });
    }

    // Create a readable stream
    const stream = createReadStream(filePath);

    // Return streaming response
    return new NextResponse(stream as any, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  } catch (error) {
    console.error('Error streaming HTML:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
