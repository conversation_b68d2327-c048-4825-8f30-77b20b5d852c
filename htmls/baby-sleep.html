<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>宝宝睡眠指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }

    .accordion-button.open .accordion-arrow {
      transform: rotate(180deg);
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }

    .chart-container {
      position: relative;
      width: 100%;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      height: 300px;
    }

    @media (min-width: 640px) {
      .chart-container {
        height: 350px;
      }
    }

    .allergen-card {
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .allergen-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .modal {
      display: none;
    }

    .modal.active {
      display: flex;
    }

    .flowchart-item {
      border: 2px solid #D4E2D4;
      background-color: #F0F5F0;
    }

    .flowchart-arrow {
      color: #77B0AA;
    }
  </style>
</head>

<body>


  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">宝宝睡眠指南</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 哄睡方法 -->
      <section id="sleep_methods" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">哄睡方法</h2>

        <!-- 核心哄睡技巧 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">😴 如何让宝宝更快入睡</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">💡 核心原则</h4>
            <p class="text-lg mb-4">帮助宝宝快速入睡的关键在于<strong>营造舒适环境、建立睡前仪式、掌握有效技巧</strong>。每个宝宝都有自己的喜好，需要耐心找到最适合的方法。</p>
          </div>

          <!-- 快速哄睡方法卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
              <h4 class="text-xl font-bold text-blue-800 mb-4">🤗 安抚技巧</h4>
              <ul class="space-y-3 text-gray-700">
                <li><strong>轻拍安抚：</strong>轻柔有节奏地拍背或拍胸</li>
                <li><strong>轻摇摆动：</strong>缓慢左右摇摆，模拟子宫环境</li>
                <li><strong>嘘嘘声：</strong>发出"嘘"声，安抚宝宝情绪</li>
                <li><strong>肌肤接触：</strong>贴身拥抱，传递安全感</li>
              </ul>
            </div>

            <div class="bg-green-50 p-6 rounded-lg border-2 border-green-200">
              <h4 class="text-xl font-bold text-green-800 mb-4">🌙 环境营造</h4>
              <ul class="space-y-3 text-gray-700">
                <li><strong>调暗光线：</strong>使用暖色小夜灯或关闭主灯</li>
                <li><strong>控制温度：</strong>保持室温20-22℃，不要过热</li>
                <li><strong>降低噪音：</strong>营造安静或播放白噪音</li>
                <li><strong>整理环境：</strong>收拾玩具，减少刺激</li>
              </ul>
            </div>

            <div class="bg-purple-50 p-6 rounded-lg border-2 border-purple-200">
              <h4 class="text-xl font-bold text-purple-800 mb-4">🍼 睡前准备</h4>
              <ul class="space-y-3 text-gray-700">
                <li><strong>喂奶时机：</strong>避免喂太饱或饥饿入睡</li>
                <li><strong>换尿布：</strong>确保干爽舒适</li>
                <li><strong>穿着舒适：</strong>选择柔软透气的睡衣</li>
                <li><strong>清洁护理：</strong>简单清洁脸部和手脚</li>
              </ul>
            </div>
          </div>

          <!-- 分年龄段哄睡技巧 -->
          <div class="space-y-6">
            <h4 class="text-2xl font-bold text-[#A57C4F] mb-4">👶 分年龄段哄睡技巧</h4>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-yellow-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-yellow-800 mb-4">0-3个月新生儿</h5>
                <div class="space-y-3 text-gray-700">
                  <p><strong>🎯 重点：</strong>模拟子宫环境，满足基本需求</p>
                  <ul class="space-y-2 ml-4">
                    <li>• <strong>包裹安抚：</strong>用小被子轻轻包裹，给予安全感</li>
                    <li>• <strong>节奏摇摆：</strong>轻柔缓慢的摇摆动作</li>
                    <li>• <strong>白噪音：</strong>吹风机、吸尘器声音或专用APP</li>
                    <li>• <strong>奶嘴安抚：</strong>满足吸吮需求（母乳喂养建议1个月后）</li>
                    <li>• <strong>贴身接触：</strong>肌肤相贴，听到妈妈心跳</li>
                  </ul>
                </div>
              </div>

              <div class="bg-orange-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-orange-800 mb-4">4-6个月</h5>
                <div class="space-y-3 text-gray-700">
                  <p><strong>🎯 重点：</strong>建立睡眠仪式，培养自主入睡</p>
                  <ul class="space-y-2 ml-4">
                    <li>• <strong>固定流程：</strong>洗澡→喂奶→讲故事→睡觉</li>
                    <li>• <strong>逐渐放手：</strong>放在床上时还有点清醒</li>
                    <li>• <strong>安抚物品：</strong>小毛巾、安抚玩具</li>
                    <li>• <strong>轻拍安抚：</strong>在床上轻拍，逐渐减少</li>
                    <li>• <strong>响应哭声：</strong>短暂等待后再安抚</li>
                  </ul>
                </div>
              </div>

              <div class="bg-teal-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-teal-800 mb-4">7-12个月</h5>
                <div class="space-y-3 text-gray-700">
                  <p><strong>🎯 重点：</strong>独立入睡，处理分离焦虑</p>
                  <ul class="space-y-2 ml-4">
                    <li>• <strong>睡前仪式：</strong>15-30分钟固定流程</li>
                    <li>• <strong>分离练习：</strong>逐渐延长离开时间</li>
                    <li>• <strong>自我安抚：</strong>允许宝宝自己找到安抚方式</li>
                    <li>• <strong>一致性：</strong>所有照料者使用相同方法</li>
                    <li>• <strong>安全感：</strong>固定的安抚物品很重要</li>
                  </ul>
                </div>
              </div>

              <div class="bg-pink-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-pink-800 mb-4">1-2岁幼儿</h5>
                <div class="space-y-3 text-gray-700">
                  <p><strong>🎯 重点：</strong>建立规则，应对睡眠阻抗</p>
                  <ul class="space-y-2 ml-4">
                    <li>• <strong>明确边界：</strong>温柔而坚定地执行睡眠时间</li>
                    <li>• <strong>选择权：</strong>让孩子选择睡衣、故事书</li>
                    <li>• <strong>预告时间：</strong>"还有5分钟就要睡觉了"</li>
                    <li>• <strong>避免刺激：</strong>睡前1小时避免兴奋活动</li>
                    <li>• <strong>耐心引导：</strong>理解这是正常的发展阶段</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div class="warning-box p-4 rounded-lg mt-6">
            <h5 class="font-bold text-red-800 mb-2">💡 温馨提示</h5>
            <p class="text-sm text-red-700">每个宝宝都有自己的节奏，不要急于求成。如果某种方法不奏效，试试其他的，或者给宝宝更多时间适应。</p>
          </div>
        </div>

        <!-- 常见睡眠问题解决 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">😫 常见睡眠问题解决</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">🔧 问题分析与解决</h4>
            <p class="text-lg mb-4">宝宝睡眠问题很常见，关键是找到<strong>根本原因并采取针对性方法</strong>。大部分问题都能通过调整得到改善。</p>
          </div>

          <!-- 常见睡眠问题 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="bg-red-50 p-6 rounded-lg border-2 border-red-200">
              <h4 class="text-xl font-bold text-red-800 mb-4">😢 入睡困难</h4>
              <div class="space-y-3">
                <p class="text-sm text-gray-600"><strong>常见表现：</strong>放到床上就哭、需要抱着才能睡、折腾很久才睡着</p>
                <div class="space-y-2">
                  <p class="text-sm"><strong>🔍 可能原因：</strong></p>
                  <ul class="text-xs text-gray-700 ml-4 space-y-1">
                    <li>• 过度刺激：白天玩得太兴奋</li>
                    <li>• 睡眠时机：错过了最佳睡眠窗口</li>
                    <li>• 环境因素：太亮、太吵、温度不适</li>
                    <li>• 饥饿不适：太饿或太饱</li>
                  </ul>
                </div>
                <div class="space-y-2">
                  <p class="text-sm"><strong>💡 解决方法：</strong></p>
                  <ul class="text-xs text-green-700 ml-4 space-y-1">
                    <li>• 观察宝宝困倦信号，及时哄睡</li>
                    <li>• 营造安静昏暗的睡眠环境</li>
                    <li>• 建立固定的睡前仪式</li>
                    <li>• 睡前1小时减少刺激活动</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
              <h4 class="text-xl font-bold text-blue-800 mb-4">🌙 夜醒频繁</h4>
              <div class="space-y-3">
                <p class="text-sm text-gray-600"><strong>常见表现：</strong>夜里醒来多次、需要安抚才能继续睡</p>
                <div class="space-y-2">
                  <p class="text-sm"><strong>🔍 可能原因：</strong></p>
                  <ul class="text-xs text-gray-700 ml-4 space-y-1">
                    <li>• 生理需求：饿了、尿了、冷了热了</li>
                    <li>• 睡眠能力：还没学会自主接觉</li>
                    <li>• 环境变化：声音、光线变化</li>
                    <li>• 成长期：猛长期、学习新技能</li>
                  </ul>
                </div>
                <div class="space-y-2">
                  <p class="text-sm"><strong>💡 解决方法：</strong></p>
                  <ul class="text-xs text-green-700 ml-4 space-y-1">
                    <li>• 先排除生理需求（饿、冷、尿湿）</li>
                    <li>• 给宝宝一些时间自己接觉</li>
                    <li>• 夜间保持环境一致性</li>
                    <li>• 白天练习自主入睡能力</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="bg-yellow-50 p-6 rounded-lg border-2 border-yellow-200">
              <h4 class="text-xl font-bold text-yellow-800 mb-4">⏰ 早醒问题</h4>
              <div class="space-y-3">
                <p class="text-sm text-gray-600"><strong>常见表现：</strong>凌晨4-5点就醒来，无法继续睡觉</p>
                <div class="space-y-2">
                  <p class="text-sm"><strong>🔍 可能原因：</strong></p>
                  <ul class="text-xs text-gray-700 ml-4 space-y-1">
                    <li>• 晨光刺激：房间太亮</li>
                    <li>• 生物钟：睡得太早</li>
                    <li>• 白天小睡：睡得太多或太晚</li>
                    <li>• 环境因素：早晨的声音</li>
                  </ul>
                </div>
                <div class="space-y-2">
                  <p class="text-sm"><strong>💡 解决方法：</strong></p>
                  <ul class="text-xs text-green-700 ml-4 space-y-1">
                    <li>• 使用遮光窗帘，保持房间黑暗</li>
                    <li>• 调整白天小睡时间和时长</li>
                    <li>• 推迟晚上入睡时间15-30分钟</li>
                    <li>• 早醒时暂时不要立即响应</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="bg-purple-50 p-6 rounded-lg border-2 border-purple-200">
              <h4 class="text-xl font-bold text-purple-800 mb-4">🍼 睡眠关联</h4>
              <div class="space-y-3">
                <p class="text-sm text-gray-600"><strong>常见表现：</strong>只有奶睡、抱睡才能入睡，放下就醒</p>
                <div class="space-y-2">
                  <p class="text-sm"><strong>🔍 可能原因：</strong></p>
                  <ul class="text-xs text-gray-700 ml-4 space-y-1">
                    <li>• 习惯依赖：过度依赖外在帮助</li>
                    <li>• 安全感：需要身体接触才安心</li>
                    <li>• 转换困难：环境变化敏感</li>
                    <li>• 自主能力：缺乏独立入睡技能</li>
                  </ul>
                </div>
                <div class="space-y-2">
                  <p class="text-sm"><strong>💡 解决方法：</strong></p>
                  <ul class="text-xs text-green-700 ml-4 space-y-1">
                    <li>• 逐步减少依赖，慢慢过渡</li>
                    <li>• 在床上进行安抚，培养床感</li>
                    <li>• 引入过渡物品（小毛巾、安抚玩具）</li>
                    <li>• 睡醒时还在同样的环境中</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

      </section>

      <!-- 睡眠规律 -->
      <section id="sleep_routine" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">睡眠规律</h2>

        <!-- 建立睡眠规律 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">⏰ 建立健康的睡眠规律</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">🎯 规律的重要性</h4>
            <p class="text-lg">良好的睡眠规律是宝宝健康发育的基石。<strong>一致的作息时间</strong>能帮助调节生物钟，提高睡眠质量，让全家都能休息得更好。</p>
          </div>

          <!-- 分年龄睡眠时间表 -->
          <div class="space-y-6 mb-8">
            <h4 class="text-2xl font-bold text-[#A57C4F] mb-4">📊 不同年龄的睡眠需求</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-blue-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-blue-800 mb-4">0-3个月新生儿</h5>
                <div class="space-y-3">
                  <p><strong>📈 总睡眠时间：</strong>14-17小时/天</p>
                  <p><strong>💤 夜间睡眠：</strong>8-9小时（分段）</p>
                  <p><strong>☀️ 白天小睡：</strong>6-8小时（4-5次）</p>
                  <p><strong>⏰ 特点：</strong>昼夜不分，每2-3小时醒来</p>
                  <div class="bg-blue-100 p-3 rounded mt-3">
                    <p class="text-sm"><strong>💡 建议：</strong>不强求规律，按需哄睡，白天多接触自然光</p>
                  </div>
                </div>
              </div>

              <div class="bg-green-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-green-800 mb-4">4-6个月</h5>
                <div class="space-y-3">
                  <p><strong>📈 总睡眠时间：</strong>12-15小时/天</p>
                  <p><strong>💤 夜间睡眠：</strong>10-12小时（可能夜醒1-2次）</p>
                  <p><strong>☀️ 白天小睡：</strong>2-4小时（3次）</p>
                  <p><strong>⏰ 特点：</strong>开始建立昼夜节律</p>
                  <div class="bg-green-100 p-3 rounded mt-3">
                    <p class="text-sm"><strong>💡 建议：</strong>开始培养固定睡眠时间，减少夜间喂奶</p>
                  </div>
                </div>
              </div>

              <div class="bg-yellow-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-yellow-800 mb-4">7-12个月</h5>
                <div class="space-y-3">
                  <p><strong>📈 总睡眠时间：</strong>12-14小时/天</p>
                  <p><strong>💤 夜间睡眠：</strong>10-12小时（整夜睡眠）</p>
                  <p><strong>☀️ 白天小睡：</strong>2-3小时（2次）</p>
                  <p><strong>⏰ 特点：</strong>夜间睡眠更稳定</p>
                  <div class="bg-yellow-100 p-3 rounded mt-3">
                    <p class="text-sm"><strong>💡 建议：</strong>固定上午和下午小睡时间，晚上7-8点入睡</p>
                  </div>
                </div>
              </div>

              <div class="bg-purple-50 p-6 rounded-lg">
                <h5 class="text-xl font-bold text-purple-800 mb-4">1-2岁</h5>
                <div class="space-y-3">
                  <p><strong>📈 总睡眠时间：</strong>11-13小时/天</p>
                  <p><strong>💤 夜间睡眠：</strong>10-12小时</p>
                  <p><strong>☀️ 白天小睡：</strong>1-2小时（1-2次）</p>
                  <p><strong>⏰ 特点：</strong>可能出现睡眠阻抗</p>
                  <div class="bg-purple-100 p-3 rounded mt-3">
                    <p class="text-sm"><strong>💡 建议：</strong>坚持规律，应对分离焦虑，18个月后可能只需1次小睡</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 建立作息时间表 -->
          <div class="bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold text-teal-800 mb-4">📅 制定个性化作息时间表</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 class="font-bold text-teal-700 mb-3">🌅 理想的一日作息（6-12个月）</h5>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between"><span>6:30-7:00</span><span>晨起</span></div>
                  <div class="flex justify-between"><span>7:00-8:00</span><span>晨奶 + 早餐</span></div>
                  <div class="flex justify-between"><span>9:00-10:30</span><span>上午小睡</span></div>
                  <div class="flex justify-between"><span>11:00-12:00</span><span>活动 + 午餐</span></div>
                  <div class="flex justify-between"><span>13:00-15:00</span><span>下午小睡</span></div>
                  <div class="flex justify-between"><span>15:30-17:30</span><span>活动 + 晚餐</span></div>
                  <div class="flex justify-between"><span>18:00-19:00</span><span>睡前仪式</span></div>
                  <div class="flex justify-between"><span>19:00-6:30</span><span>夜间睡眠</span></div>
                </div>
              </div>

              <div>
                <h5 class="font-bold text-teal-700 mb-3">⚡ 调整作息的技巧</h5>
                <ul class="text-sm space-y-2">
                  <li><strong>循序渐进：</strong>每次调整15分钟，给宝宝适应时间</li>
                  <li><strong>观察信号：</strong>根据宝宝的困倦表现调整时间</li>
                  <li><strong>保持弹性：</strong>生病或特殊情况时可以灵活调整</li>
                  <li><strong>全家配合：</strong>所有照料者都要遵守相同时间表</li>
                  <li><strong>记录追踪：</strong>用睡眠日记记录效果并调整</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 睡眠环境优化 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="p-4 border-2 border-yellow-200 rounded-lg bg-yellow-50">
              <h5 class="font-bold text-yellow-800 mb-3">🌙 光线管理</h5>
              <ul class="text-sm text-yellow-700 space-y-2">
                <li>• 白天：充足自然光，帮助建立昼夜节律</li>
                <li>• 傍晚：逐渐调暗光线，准备睡眠</li>
                <li>• 夜间：保持黑暗，使用小夜灯导航</li>
                <li>• 早晨：用自然光或明亮灯光唤醒</li>
              </ul>
            </div>

            <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
              <h5 class="font-bold text-blue-800 mb-3">🌡️ 温度湿度</h5>
              <ul class="text-sm text-blue-700 space-y-2">
                <li>• 室温：保持在18-22℃</li>
                <li>• 湿度：维持40-60%相对湿度</li>
                <li>• 通风：保持空气流通但避免直吹</li>
                <li>• 衣着：根据温度选择合适睡衣</li>
              </ul>
            </div>

            <div class="p-4 border-2 border-purple-200 rounded-lg bg-purple-50">
              <h5 class="font-bold text-purple-800 mb-3">🔇 声音环境</h5>
              <ul class="text-sm text-purple-700 space-y-2">
                <li>• 白天：正常生活音量，不刻意安静</li>
                <li>• 夜间：保持安静，避免突然噪音</li>
                <li>• 白噪音：可以帮助遮蔽环境声音</li>
                <li>• 一致性：睡眠环境声音要相对稳定</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 睡眠训练方法 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🎯 科学的睡眠训练方法</h3>

          <div class="warning-box p-4 rounded-lg mb-6">
            <h5 class="font-bold text-red-800 mb-2">⚠️ 重要提醒</h5>
            <p class="text-sm text-red-700">睡眠训练适合4-6个月以上的健康宝宝。开始前请确保宝宝身体健康，没有睡眠医学问题。</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-green-50 p-6 rounded-lg border-2 border-green-200">
              <h4 class="text-xl font-bold text-green-800 mb-4">🤗 温和方法（推荐）</h4>
              <div class="space-y-4">
                <div>
                  <h5 class="font-bold mb-2">渐进式调整法：</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 每天减少5-10分钟安抚时间</li>
                    <li>• 逐渐从床上安抚转为床边安抚</li>
                    <li>• 慢慢增加宝宝自主入睡时间</li>
                    <li>• 给予宝宝适应期，通常需要1-2周</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-bold mb-2">椅子法：</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 第一周：坐在床边陪伴</li>
                    <li>• 第二周：坐在房间中央</li>
                    <li>• 第三周：坐在门口</li>
                    <li>• 第四周：在门外，逐渐完全离开</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="bg-orange-50 p-6 rounded-lg border-2 border-orange-200">
              <h4 class="text-xl font-bold text-orange-800 mb-4">⏱️ 定时检查法</h4>
              <div class="space-y-4">
                <div>
                  <h5 class="font-bold mb-2">操作步骤：</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 完成睡前仪式后放下宝宝</li>
                    <li>• 如果哭闹，等待3分钟再进入安抚</li>
                    <li>• 简单安抚1-2分钟后离开</li>
                    <li>• 逐渐延长等待时间（5分钟、7分钟...）</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-bold mb-2">注意事项：</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 进入房间时保持冷静温和</li>
                    <li>• 不要重新抱起宝宝</li>
                    <li>• 安抚时间要简短</li>
                    <li>• 坚持一致的方法至少一周</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 安全睡眠 -->
      <section id="safe_sleep" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">安全睡眠</h2>

        <!-- 安全睡眠基础 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🛡️ 安全睡眠的基本原则</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">🔍 为什么安全睡眠这么重要？</h4>
            <p class="text-lg mb-4">正确的睡眠环境和姿势能够<strong>大大降低婴儿猝死综合征的风险</strong>，让宝宝睡得安全又舒适。这些简单的规则能救命！</p>
          </div>

          <!-- 核心安全原则 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-red-50 p-6 rounded-lg border-2 border-red-200">
              <h4 class="text-xl font-bold text-red-800 mb-4">😴 仰卧睡眠</h4>
              <div class="text-center mb-3">
                <div class="text-5xl">👶</div>
                <p class="text-sm text-gray-600 mt-2">这是最重要的一条</p>
              </div>
              <ul class="space-y-2 text-sm">
                <li>• <strong>每次睡觉都要仰睡</strong>，包括小憩</li>
                <li>• 不要侧睡或趴睡</li>
                <li>• 直到宝宝满1岁</li>
                <li>• 会翻身后可以自然选择睡姿</li>
              </ul>
              <div class="bg-red-100 p-3 rounded mt-3">
                <p class="text-xs text-red-700"><strong>提醒：</strong>仰睡能降低婴儿猝死风险50-80%</p>
              </div>
            </div>

            <div class="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
              <h4 class="text-xl font-bold text-blue-800 mb-4">🛏️ 结实床面</h4>
              <div class="text-center mb-3">
                <div class="text-5xl">🛏️</div>
                <p class="text-sm text-gray-600 mt-2">硬一点更安全</p>
              </div>
              <ul class="space-y-2 text-sm">
                <li>• 使用结实的婴儿床垫</li>
                <li>• 床垫要紧贴床架</li>
                <li>• 不要在沙发、软椅上睡</li>
                <li>• 避免太软的床垫或表面</li>
              </ul>
              <div class="bg-blue-100 p-3 rounded mt-3">
                <p class="text-xs text-blue-700"><strong>测试：</strong>用手按压床垫，应该迅速回弹</p>
              </div>
            </div>

            <div class="bg-green-50 p-6 rounded-lg border-2 border-green-200">
              <h4 class="text-xl font-bold text-green-800 mb-4">🚫 床面清洁</h4>
              <div class="text-center mb-3">
                <div class="text-5xl">🧸</div>
                <p class="text-sm text-gray-600 mt-2">什么都不要放</p>
              </div>
              <ul class="space-y-2 text-sm">
                <li>• 不放毛绒玩具</li>
                <li>• 不用枕头和厚被子</li>
                <li>• 不放防撞垫</li>
                <li>• 只需要紧贴的床单</li>
              </ul>
              <div class="bg-green-100 p-3 rounded mt-3">
                <p class="text-xs text-green-700"><strong>原则：</strong>光滑、干净、什么都没有</p>
              </div>
            </div>
          </div>

          <!-- 安全睡眠环境 -->
          <div class="bg-gradient-to-r from-cyan-50 to-blue-50 p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold text-cyan-800 mb-4">🏠 打造安全的睡眠环境</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 class="font-bold text-cyan-700 mb-3">✅ 这样做是对的</h5>
                <ul class="space-y-2 text-sm">
                  <li>🛡️ <strong>同屋不同床：</strong>把婴儿床放在大人房间里</li>
                  <li>🌡️ <strong>合适温度：</strong>保持房间温度18-22度</li>
                  <li>🚭 <strong>禁烟环境：</strong>家里和车里都不能吸烟</li>
                  <li>👕 <strong>睡衣睡袋：</strong>用睡袋代替被子</li>
                  <li>📱 <strong>监控设备：</strong>可以用婴儿监控器</li>
                  <li>🍼 <strong>母乳喂养：</strong>能降低猝死风险</li>
                </ul>
              </div>

              <div>
                <h5 class="font-bold text-red-700 mb-3">❌ 这些千万不要做</h5>
                <ul class="space-y-2 text-sm">
                  <li>🛏️ <strong>不要同床睡：</strong>大人床对宝宝不安全</li>
                  <li>🚫 <strong>不要趴睡侧睡：</strong>只能仰睡</li>
                  <li>🧸 <strong>不要放杂物：</strong>床上什么都不能放</li>
                  <li>🌡️ <strong>不要过热：</strong>摸后颈不出汗就行</li>
                  <li>🚗 <strong>不要车里睡：</strong>车座椅只用于乘车</li>
                  <li>🛋️ <strong>不要沙发睡：</strong>沙发缝隙有危险</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="warning-box p-4 rounded-lg mt-6">
            <h5 class="font-bold text-red-800 mb-2">🚨 紧急提醒</h5>
            <p class="text-sm text-red-700">如果发现宝宝脸色发青、呼吸急促或停止、反应迟钝，立即叫救护车！学习婴儿心肺复苏术也很重要。</p>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">


          <!-- 常见安全问题解答 -->
          <div class="space-y-4">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">❓ 家长常问的安全问题</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="border-2 border-gray-200 rounded-lg p-4">
                <h5 class="font-bold text-gray-800 mb-2">Q: 宝宝仰睡会被口水呛到吗？</h5>
                <p class="text-sm text-gray-700">A: 不会的！宝宝的身体构造能自然处理口水和反流，仰睡时反而更安全。侧睡和趴睡才容易被堵住呼吸道。</p>
              </div>

              <div class="border-2 border-gray-200 rounded-lg p-4">
                <h5 class="font-bold text-gray-800 mb-2">Q: 宝宝睡觉时头老是偏一边怎么办？</h5>
                <p class="text-sm text-gray-700">A: 这很正常！可以每次睡觉时轮换宝宝的头位方向，白天多让宝宝趴着玩（清醒且有人看护）来锻炼脖子肌肉。</p>
              </div>

              <div class="border-2 border-gray-200 rounded-lg p-4">
                <h5 class="font-bold text-gray-800 mb-2">Q: 什么时候可以给宝宝用枕头？</h5>
                <p class="text-sm text-gray-700">A: 至少等到1岁以后！小宝宝的头和身体比例不同，仰睡时头部自然贴合床面，不需要枕头。太早用枕头反而不安全。</p>
              </div>

              <div class="border-2 border-gray-200 rounded-lg p-4">
                <h5 class="font-bold text-gray-800 mb-2">Q: 宝宝感冒鼻塞还能仰睡吗？</h5>
                <p class="text-sm text-gray-700">A: 能！可以用生理盐水滴鼻子，使用加湿器，或者稍微抬高床头（垫在床垫下面，不是用枕头），但仍要保持仰睡。</p>
              </div>
            </div>
          </div>

          <div class="warning-box p-4 rounded-lg mt-6">
            <h5 class="font-bold text-red-800 mb-2">💡 温馨提醒</h5>
            <p class="text-sm text-red-700">每个照看宝宝的人（爷爷奶奶、保姆、亲戚朋友）都要知道这些安全睡眠规则。不要因为"以前都这样做"就忽视安全！</p>
          </div>
        </div>
      </section>

      <!-- 睡眠发育 -->
      <section id="sleep_development" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">睡眠发育</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">📈 宝宝睡眠能力的发展</h3>
          <p class="text-lg">了解宝宝在不同阶段的睡眠发育特点，能帮助您<strong>设置合理期望，选择适合的方法</strong>。每个宝宝的发育节奏都不同，耐心是关键。</p>
        </div>



        <!-- 睡眠发育时间线 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">⏱️ 宝宝睡眠发育时间线</h3>

          <!-- 0-3个月 -->
          <div class="mb-8">
            <div class="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
              <h4 class="text-xl font-bold text-blue-800 mb-4">👶 0-3个月：适应期</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold text-blue-700 mb-2">🧠 大脑发育特点</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 昼夜节律尚未建立</li>
                    <li>• 睡眠周期只有50-60分钟</li>
                    <li>• REM（快速眼动）睡眠占50%</li>
                    <li>• 容易被声音和动作惊醒</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-bold text-blue-700 mb-2">😴 睡眠表现</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 每天睡14-17小时</li>
                    <li>• 每次睡1-3小时</li>
                    <li>• 饿了就醒，累了就睡</li>
                    <li>• 需要频繁安抚</li>
                  </ul>
                </div>
              </div>
              <div class="bg-blue-100 p-3 rounded mt-4">
                <p class="text-sm text-blue-800"><strong>这个阶段要做的：</strong>观察宝宝的困倦信号，提供安全舒适的睡眠环境，不要期望规律的睡眠时间。</p>
              </div>
            </div>
          </div>

          <!-- 4-6个月 -->
          <div class="mb-8">
            <div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500">
              <h4 class="text-xl font-bold text-green-800 mb-4">🌱 4-6个月：发展期</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold text-green-700 mb-2">🧠 大脑发育特点</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 开始分泌褪黑素</li>
                    <li>• 昼夜节律逐渐形成</li>
                    <li>• 睡眠周期延长到90分钟</li>
                    <li>• 深度睡眠时间增加</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-bold text-green-700 mb-2">😴 睡眠表现</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 每天睡12-15小时</li>
                    <li>• 夜间可睡6-8小时</li>
                    <li>• 白天需要3次小睡</li>
                    <li>• 开始有固定睡眠时间</li>
                  </ul>
                </div>
              </div>
              <div class="bg-green-100 p-3 rounded mt-4">
                <p class="text-sm text-green-800"><strong>这个阶段要做的：</strong>建立睡前仪式，培养自主入睡能力，可以开始温和的睡眠训练。</p>
              </div>
            </div>
          </div>

          <!-- 7-12个月 -->
          <div class="mb-8">
            <div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500">
              <h4 class="text-xl font-bold text-yellow-800 mb-4">🚀 7-12个月：成熟期</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold text-yellow-700 mb-2">🧠 大脑发育特点</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 昼夜节律基本建立</li>
                    <li>• 具备整夜睡眠能力</li>
                    <li>• 大脑发育可能影响睡眠</li>
                    <li>• 分离焦虑开始出现</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-bold text-yellow-700 mb-2">😴 睡眠表现</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 每天睡12-14小时</li>
                    <li>• 夜间可睡10-12小时</li>
                    <li>• 白天2次小睡</li>
                    <li>• 可能出现睡眠退化</li>
                  </ul>
                </div>
              </div>
              <div class="bg-yellow-100 p-3 rounded mt-4">
                <p class="text-sm text-yellow-800"><strong>这个阶段要做的：</strong>坚持睡眠规律，应对分离焦虑，处理睡眠技能发展期的挑战。</p>
              </div>
            </div>
          </div>

          <!-- 1-2岁 -->
          <div class="mb-8">
            <div class="bg-purple-50 p-6 rounded-lg border-l-4 border-purple-500">
              <h4 class="text-xl font-bold text-purple-800 mb-4">🎯 1-2岁：稳定期</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold text-purple-700 mb-2">🧠 大脑发育特点</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 自我意识增强</li>
                    <li>• 语言和认知快速发展</li>
                    <li>• 可能出现睡眠阻抗</li>
                    <li>• 开始理解规则和边界</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-bold text-purple-700 mb-2">😴 睡眠表现</h5>
                  <ul class="text-sm space-y-1">
                    <li>• 每天睡11-13小时</li>
                    <li>• 夜间睡10-12小时</li>
                    <li>• 下午1次小睡</li>
                    <li>• 睡前可能要求延迟</li>
                  </ul>
                </div>
              </div>
              <div class="bg-purple-100 p-3 rounded mt-4">
                <p class="text-sm text-purple-800"><strong>这个阶段要做的：</strong>温柔而坚定地执行睡眠规则，给予选择权但保持边界，理解这是正常的发展阶段。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 睡眠数据图表 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">📊 宝宝睡眠时间图表</h3>

          <!-- 总睡眠时间图表 -->
          <div class="mb-8">
            <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">每日总睡眠时间变化</h4>
            <div class="chart-container" style="height: 250px;">
              <canvas id="totalSleepChart"></canvas>
            </div>
          </div>

          <!-- 夜间连续睡眠图表 -->
          <div class="mb-8">
            <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">夜间连续睡眠时间发展</h4>
            <div class="chart-container" style="height: 250px;">
              <canvas id="nightSleepChart"></canvas>
            </div>
          </div>

          <!-- 图表说明 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h5 class="font-bold text-gray-800 mb-2">📈 图表说明</h5>
            <ul class="text-sm text-gray-700 space-y-1">
              <li>• 蓝色区域显示正常睡眠时间范围</li>
              <li>• 实线显示平均睡眠时间</li>
              <li>• 每个宝宝的发育节奏都不同，这些只是参考</li>
              <li>• 如果宝宝健康快乐，睡眠时间稍有差异是正常的</li>
            </ul>
          </div>
        </div>

        <!-- 常见睡眠发育问题 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🤔 常见睡眠发育问题</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="p-4 border-2 border-orange-200 rounded-lg bg-orange-50">
                <h4 class="font-bold text-orange-800 mb-2">4个月睡眠退化</h4>
                <p class="text-sm text-orange-700 mb-2"><strong>表现：</strong>原本睡得很好的宝宝突然频繁夜醒</p>
                <p class="text-sm text-orange-700"><strong>原因：</strong>大脑发育，睡眠模式从新生儿转向成人模式</p>
                <p class="text-sm text-green-700 mt-2"><strong>应对：</strong>保持一致的睡眠习惯，这是暂时的，通常2-6周会改善</p>
              </div>

              <div class="p-4 border-2 border-red-200 rounded-lg bg-red-50">
                <h4 class="font-bold text-red-800 mb-2">8-10个月睡眠挑战</h4>
                <p class="text-sm text-red-700 mb-2"><strong>表现：</strong>学会坐立、爬行后不愿意躺下睡觉</p>
                <p class="text-sm text-red-700"><strong>原因：</strong>新技能兴奋期，分离焦虑开始</p>
                <p class="text-sm text-green-700 mt-2"><strong>应对：</strong>白天多练习新技能，睡前增加安抚，保持规律</p>
              </div>
            </div>

            <div class="space-y-4">
              <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
                <h4 class="font-bold text-blue-800 mb-2">18个月睡眠阻抗</h4>
                <p class="text-sm text-blue-700 mb-2"><strong>表现：</strong>要求延迟睡觉，各种借口不肯上床</p>
                <p class="text-sm text-blue-700"><strong>原因：</strong>自我意识增强，想要控制环境</p>
                <p class="text-sm text-green-700 mt-2"><strong>应对：</strong>给予有限选择，温柔而坚定执行规则</p>
              </div>

              <div class="p-4 border-2 border-purple-200 rounded-lg bg-purple-50">
                <h4 class="font-bold text-purple-800 mb-2">2岁小睡过渡</h4>
                <p class="text-sm text-purple-700 mb-2"><strong>表现：</strong>不愿意小睡或小睡时间变短</p>
                <p class="text-sm text-purple-700"><strong>原因：</strong>睡眠需求减少，准备过渡到单次小睡</p>
                <p class="text-sm text-green-700 mt-2"><strong>应对：</strong>观察疲劳信号，灵活调整小睡时间</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <footer class="text-center py-6 mt-8 border-t-2 border-gray-200">
    <p class="text-gray-500 text-sm">本睡眠指南信息仅供参考，不能替代专业医疗建议、诊断或治疗。如有严重睡眠问题或担忧，请务必咨询您的儿科医生。</p>
  </footer>

  <script>
    const sections = [
      { id: 'sleep_methods', title: '哄睡方法', icon: '😴' },
      { id: 'sleep_routine', title: '睡眠规律', icon: '⏰' },
      { id: 'safe_sleep', title: '安全睡眠', icon: '🛡️' },
      { id: 'sleep_development', title: '睡眠发育', icon: '📈' }
    ];

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

        window.scrollTo(0, 0);

        if (targetId === 'sleep_development') {
          setupCharts();
        }
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1); // 去掉#符号
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        // 默认激活第一个标签
        activateSection('sleep_methods');
      }

      // 监听hash变化（支持浏览器前进后退）
      window.addEventListener('hashchange', handleHashChange);

      // 睡眠图表设置
      function setupCharts() {
        // 确保 Chart.js 已加载
        if (typeof Chart === 'undefined') {
          console.warn('Chart.js not loaded');
          return;
        }

        // 总睡眠时间图表
        const totalSleepCtx = document.getElementById('totalSleepChart');
        if (totalSleepCtx) {
          new Chart(totalSleepCtx, {
            type: 'line',
            data: {
              labels: ['新生儿', '1个月', '2个月', '3个月', '4个月', '5个月', '6个月', '9个月', '12个月', '18个月', '24个月'],
              datasets: [{
                label: '平均睡眠时间',
                data: [16, 15.5, 15, 14.5, 14, 13.5, 13, 12.5, 12, 11.5, 11],
                borderColor: '#8FBC8F',
                backgroundColor: 'rgba(143, 188, 143, 0.1)',
                fill: true,
                tension: 0.4
              }, {
                label: '上限',
                data: [17, 16.5, 16, 15.5, 15, 14.5, 14, 13.5, 13, 12.5, 12],
                borderColor: '#D4E2D4',
                backgroundColor: 'transparent',
                borderDash: [5, 5]
              }, {
                label: '下限',
                data: [14, 14, 13.5, 13, 12.5, 12, 11.5, 11, 10.5, 10.5, 10],
                borderColor: '#D4E2D4',
                backgroundColor: 'transparent',
                borderDash: [5, 5]
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: false
                },
                legend: {
                  display: true,
                  position: 'bottom'
                }
              },
              scales: {
                y: {
                  beginAtZero: false,
                  min: 8,
                  max: 18,
                  title: {
                    display: true,
                    text: '小时'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: '年龄'
                  }
                }
              }
            }
          });
        }

        // 夜间连续睡眠图表
        const nightSleepCtx = document.getElementById('nightSleepChart');
        if (nightSleepCtx) {
          new Chart(nightSleepCtx, {
            type: 'line',
            data: {
              labels: ['新生儿', '1个月', '2个月', '3个月', '4个月', '5个月', '6个月', '9个月', '12个月', '18个月', '24个月'],
              datasets: [{
                label: '夜间连续睡眠',
                data: [2, 3, 4, 5, 6, 7, 8, 9, 10, 10, 10],
                borderColor: '#77B0AA',
                backgroundColor: 'rgba(119, 176, 170, 0.1)',
                fill: true,
                tension: 0.4
              }, {
                label: '理想目标',
                data: [null, null, null, null, 6, 8, 10, 11, 11, 11, 11],
                borderColor: '#A57C4F',
                backgroundColor: 'transparent',
                borderDash: [10, 5]
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: false
                },
                legend: {
                  display: true,
                  position: 'bottom'
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  max: 12,
                  title: {
                    display: true,
                    text: '小时'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: '年龄'
                  }
                }
              }
            }
          });
        }
      }
    });
  </script>
</body>

</html>