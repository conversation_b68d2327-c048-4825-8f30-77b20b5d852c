<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>母乳喂养新手指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }

    .accordion-button.open .accordion-arrow {
      transform: rotate(180deg);
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }
  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">母乳喂养</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 母乳喂养 -->
      <section id="feeding_basics" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">母乳喂养</h2>

        <!-- 1. 喂养方法（最重要，放在最前面） -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">1. 喂养方法</h3>
          
          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">🤱 按需喂养原则</h4>
            <p class="text-lg mb-4">母乳喂养应该根据宝宝的需要进行，而不是严格按照时间表。</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-bold mb-2">⏰ 新生儿喂养频率：</h5>
                <ul class="space-y-1 text-gray-700">
                  <li>• 每天8-12次</li>
                  <li>• 每1.5-3小时一次</li>
                  <li>• 夜间也需要喂养</li>
                  <li>• 每次15-45分钟</li>
                </ul>
              </div>
            </div>
          </div>

          <h4 class="text-xl font-bold text-[#A57C4F] mb-4">正确衔乳技巧</h4>
          
          <div class="warning-box p-4 rounded-lg mb-4">
            <h5 class="text-lg font-bold mb-2">⚠️ 重要提醒</h5>
            <p class="text-base">正确的衔乳<strong>不应该疼痛</strong>！如果疼，立即调整姿势。</p>
          </div>

          <div class="mb-6">
            <h5 class="text-lg font-bold text-[#A57C4F] mb-3">衔乳四步法</h5>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div
                  class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">
                  1</div>
                <h6 class="font-bold text-sm mb-1">舒适坐好</h6>
                <p class="text-xs text-gray-600">用枕头支撑，让宝宝靠近你</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div
                  class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">
                  2</div>
                <h6 class="font-bold text-sm mb-1">对齐身体</h6>
                <p class="text-xs text-gray-600">宝宝胸贴胸，头与身体成直线</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div
                  class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">
                  3</div>
                <h6 class="font-bold text-sm mb-1">引诱张嘴</h6>
                <p class="text-xs text-gray-600">乳头碰上唇，等他大张嘴</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div
                  class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">
                  4</div>
                <h6 class="font-bold text-sm mb-1">快速贴合</h6>
                <p class="text-xs text-gray-600">抱向乳房，含住大部分乳晕</p>
              </div>
            </div>
          </div>

          <h4 class="text-xl font-bold text-[#A57C4F] mb-4">选择舒适的喂养姿势</h4>
          <p class="text-gray-600 mb-4">选择适合自己和宝宝的姿势，确保长时间喂奶时也能保持舒适。</p>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-[#A57C4F] transition text-center">
              <div class="text-4xl mb-3">🤱</div>
              <h5 class="font-bold text-lg mb-2">摇篮式</h5>
              <p class="text-sm text-gray-600">最常用，适合熟练后使用</p>
              <p class="text-xs text-gray-500 mt-2">宝宝头枕在手臂弯曲处</p>
            </div>
            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-[#A57C4F] transition text-center">
              <div class="text-4xl mb-3">🔄</div>
              <h5 class="font-bold text-lg mb-2">交叉摇篮式</h5>
              <p class="text-sm text-gray-600">新生儿首选，控制更好</p>
              <p class="text-xs text-gray-500 mt-2">用对侧手臂支撑宝宝</p>
            </div>
            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-[#A57C4F] transition text-center">
              <div class="text-4xl mb-3">🏈</div>
              <h5 class="font-bold text-lg mb-2">橄榄球式</h5>
              <p class="text-sm text-gray-600">剖腹产妈妈友好</p>
              <p class="text-xs text-gray-500 mt-2">宝宝在身体一侧，避开腹部</p>
            </div>
            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-[#A57C4F] transition text-center">
              <div class="text-4xl mb-3">🛌</div>
              <h5 class="font-bold text-lg mb-2">侧卧式</h5>
              <p class="text-sm text-gray-600">夜间喂奶必备</p>
              <p class="text-xs text-gray-500 mt-2">妈妈和宝宝都侧躺</p>
            </div>
          </div>
          
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h5 class="font-bold text-blue-800 mb-2">💡 选择姿势的小贴士：</h5>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• 新手妈妈建议先从交叉摇篮式开始</li>
              <li>• 每次喂奶可以尝试不同姿势，避免乳房同一部位反复受压</li>
              <li>• 使用哺乳枕或枕头支撑，减轻手臂负担</li>
              <li>• 确保宝宝的头、颈、身体呈一条直线</li>
            </ul>
          </div>
        </div>

        <!-- 2. 识别宝宝的喂养信号 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">2. 识别宝宝的喂养信号</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">宝宝饿了的信号</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>扭动身体，眼球转动</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>舔嘴唇、咂嘴</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>小手放进嘴里</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>头转向两侧寻找</li>
                <li class="text-red-600 font-bold">❌ 哭闹（已经太晚了）</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">宝宝吃饱的信号</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>吸吮变慢或停止</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>主动松开乳头</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>身体和手掌放松</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>满足地睡着</li>
              </ul>
            </div>
          </div>
        </div>



        <!-- 总结：成功母乳喂养的关键 -->
      </section>

      <!-- 挤奶与存储 -->
      <section id="pumping_storage" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">挤奶与存储</h2>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">挤奶技巧</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-xl font-bold mb-4">手挤奶方法</h4>
              <ol class="space-y-2 text-gray-700">
                <li><strong>1.</strong> 清洁双手和乳房</li>
                <li><strong>2.</strong> 拇指和食指呈C形放在乳晕边缘</li>
                <li><strong>3.</strong> 向胸壁推压，然后压缩</li>
                <li><strong>4.</strong> 松开手指，重复动作</li>
                <li><strong>5.</strong> 环绕乳房，各个方向都要挤</li>
              </ol>
            </div>
            <div>
              <h4 class="text-xl font-bold mb-4">使用吸奶器</h4>
              <ul class="space-y-2 text-gray-700">
                <li>✓ 选择合适尺寸的喇叭罩</li>
                <li>✓ 从低档位开始，逐渐调节</li>
                <li>✓ 模拟宝宝吸吮节奏</li>
                <li>✓ 每次使用15-20分钟</li>
                <li>✓ 双侧同时进行更高效</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">📝 储存记忆口诀</h3>
          <p class="text-2xl font-bold text-center">室温4小时，冷藏4天，冷冻6个月</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white p-6 rounded-lg shadow-md text-center border-t-4 border-[#EACDA4]">
            <h3 class="font-bold text-lg mb-2">室温 (25°C以下)</h3>
            <p class="text-3xl font-bold mb-2 text-[#EACDA4]">4小时</p>
            <p class="text-sm text-gray-600">最佳储存时间</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md text-center border-t-4 border-[#8FBC8F]">
            <h3 class="font-bold text-lg mb-2">冰箱冷藏 (4°C以下)</h3>
            <p class="text-3xl font-bold mb-2 text-[#8FBC8F]">4天</p>
            <p class="text-sm text-gray-600">最佳储存时间</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md text-center border-t-4 border-[#A57C4F]">
            <h3 class="font-bold text-lg mb-2">冰箱冷冻 (-18°C以下)</h3>
            <p class="text-3xl font-bold mb-2 text-[#A57C4F]">6个月</p>
            <p class="text-sm text-gray-600">最佳储存时间</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold text-[#8FBC8F] mb-4">✅ 正确做法</h3>
            <ul class="space-y-3">
              <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>用专用储奶袋或玻璃瓶</li>
              <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>标注挤奶日期和时间</li>
              <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>先进先出，用最早的</li>
              <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>解冻用温水或冷藏室</li>
              <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>分小份储存（60-120ml）</li>
            </ul>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold text-[#D99477] mb-4">❌ 危险操作</h3>
            <ul class="space-y-3">
              <li class="text-red-600 font-medium">❌ 绝不用微波炉加热</li>
              <li class="text-red-600 font-medium">❌ 解冻后不可再冷冻</li>
              <li class="text-red-600 font-medium">❌ 不要用开水直接加热</li>
              <li class="text-red-600 font-medium">❌ 不要在冰箱门上存放</li>
              <li class="text-red-600 font-medium">❌ 不要混合不同日期的奶</li>
            </ul>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-xl font-bold text-[#A57C4F] mb-4">母乳加热方法</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-bold mb-3">推荐方法</h4>
              <ul class="space-y-2 text-gray-700">
                <li>• 温水浸泡：37-40°C温水中浸泡</li>
                <li>• 温奶器：设定37°C加热</li>
                <li>• 流动温水：水龙头下冲洗加热</li>
              </ul>
            </div>
            <div>
              <h4 class="font-bold mb-3">检测温度</h4>
              <ul class="space-y-2 text-gray-700">
                <li>• 滴几滴在手腕内侧</li>
                <li>• 应该感觉温热，不烫手</li>
                <li>• 轻柔摇晃混合均匀</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- 其它问题 -->
      <section id="other_problems" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">其它问题</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">💡 解决问题的原则</h3>
          <p class="text-lg">大部分哺乳问题都源于<strong>衔乳不正确</strong>。遇到问题，首先检查衔乳姿势！</p>
        </div>

        <div id="accordion-container" class="space-y-3">
          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>乳头疼痛怎么办？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>立即行动：</strong><br>
                1. 检查衔乳姿势 - 90%疼痛都因此<br>
                2. 变换哺乳姿势<br>
                3. 喂奶后涂少量母乳<br>
                4. 使用乳头霜（羊毛脂）<br>
                <strong>记住：</strong>正确衔乳不应该疼！
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>乳房有硬块（堵奶）？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>马上处理：</strong><br>
                1. 频繁喂奶或挤奶<br>
                2. 让宝宝下巴对准硬块吸吮<br>
                3. 喂奶时轻柔按摩硬块<br>
                4. 喂前热敷3分钟，喂后冷敷<br>
                5. 变换喂奶姿势
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>担心奶水不够？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-base leading-relaxed space-y-4">
                <p>很多妈妈都会担心奶水不够，这是非常正常的！但请相信你的身体，判断奶量是否充足，不要凭感觉，而要看宝宝自己给出的“答案”。</p>
                
                <div>
                  <h4 class="font-bold text-lg text-[#8FBC8F] mb-2">✅ 判断奶量充足的“黄金标准”</h4>
                  <ul class="list-disc list-inside space-y-1">
                    <li><strong>看尿布：</strong>这是最可靠的指标！出生第5天起，每天有6片以上沉甸甸的湿尿布。</li>
                    <li><strong>看便便：</strong>每天有3-4次黄色、稀软的便便（满月后次数可能减少）。</li>
                    <li><strong>看体重：</strong>体重稳定增长（可参考生长曲线，通常每周150-200g）。</li>
                    <li><strong>看状态：</strong>喂奶时能听到吞咽声，喂完后宝宝看起来满足、放松。</li>
                  </ul>
                </div>

                <div>
                  <h4 class="font-bold text-lg text-[#D99477] mb-2">❌ 这些“假象”最会骗人</h4>
                  <ul class="list-disc list-inside space-y-1">
                    <li><strong>“我的乳房感觉软软的”：</strong>供需平衡后的正常现象，不代表没奶！</li>
                    <li><strong>“宝宝总是在哭”：</strong>哭不只代表饿，也可能是求安抚、肠胀气等。</li>
                    <li><strong>“吸奶器吸不出多少奶”：</strong>吸奶器的量 ≠ 宝宝实际吃到的量。宝宝的嘴是最高效的“吸奶器”。</li>
                  </ul>
                </div>

                <div>
                  <h4 class="font-bold text-lg text-[#A57C4F] mb-2">🚀 提升奶量的核心秘诀</h4>
                  <ul class="list-disc list-inside space-y-1">
                    <li><strong>让宝宝多吸、有效吸吮：</strong>这是最关键的！宝宝吸吮是给妈妈身体下“产奶”订单，吸得越多，产得越多。</li>
                    <li><strong>保证妈妈休息好、心情好：</strong>压力和疲劳是奶量的“隐形杀手”。</li>
                    <li><strong>营养均衡，多喝汤水：</strong>妈妈吃好喝好，奶水自然来。</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>奶水太多，宝宝呛奶？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>控制技巧：</strong><br>
                1. 半躺式喂奶<br>
                2. 先挤出一些前奶<br>
                3. 让宝宝含紧后再开始<br>
                4. 奶阵来时暂停，让奶流缓解<br>
                5. 单侧喂奶时间延长
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>宝宝拒绝吃奶？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>可能原因：</strong><br>
                • 生病或不适（鼻塞、口疮等）<br>
                • 过度刺激或环境嘈杂<br>
                • 奶流过快或过慢<br>
                • 混合喂养造成的乳头混淆<br>
                <strong>解决方法：</strong><br>
                • 营造安静舒适环境<br>
                • 尝试不同喂奶姿势<br>
                • 肌肤接触增进感情
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>夜间喂奶太累怎么办？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>省力技巧：</strong><br>
                1. 采用侧卧喂奶姿势<br>
                2. 宝宝床紧挨大床<br>
                3. 准备夜灯，避免开大灯<br>
                4. 白天多休息补眠<br>
                5. 家人轮流照顾<br>
                <strong>安全提醒：</strong>侧卧喂奶后要将宝宝放回自己的睡眠空间
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button
              class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>什么时候需要看医生？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>立即就医情况：</strong><br>
                • 发烧38.5°C以上<br>
                • 乳房红肿热痛（可能乳腺炎）<br>
                • 乳头严重破裂出血<br>
                • 宝宝体重不增或下降<br>
                • 宝宝超过6小时不排尿<br>
                • 持续疼痛影响喂奶<br>
                <strong>咨询专业人士：</strong>母乳喂养顾问、产科医生、儿科医生
              </div>
            </div>
          </div>
        </div>
      </section>


    </main>
  </div>

  <script>
    const sections = [
      { id: 'feeding_basics', title: '母乳喂养', icon: '🤱' },
      { id: 'pumping_storage', title: '挤奶与存储', icon: '🍼' },
      { id: 'other_problems', title: '其它问题', icon: '🛠️' }
    ];

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title.split('：')[0]}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

        window.scrollTo(0, 0);

        if (targetId === 'other_problems') {
          setupAccordion();
        }
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1); // 去掉#符号
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        // 默认激活第一个标签
        activateSection('feeding_basics');
      }

      // 监听hash变化（支持浏览器前进后退）
      window.addEventListener('hashchange', handleHashChange);

      // 手风琴功能
      function setupAccordion() {
        const accordionContainer = document.getElementById('accordion-container');
        if (!accordionContainer) return;

        accordionContainer.addEventListener('click', function (e) {
          const button = e.target.closest('.accordion-button');
          if (button) {
            const content = button.nextElementSibling;
            const isOpen = button.classList.contains('open');

            // 关闭所有其他的
            accordionContainer.querySelectorAll('.accordion-button').forEach(btn => {
              btn.classList.remove('open');
              btn.nextElementSibling.style.maxHeight = null;
            });

            // 切换当前的
            if (!isOpen) {
              button.classList.add('open');
              content.style.maxHeight = content.scrollHeight + "px";
            }
          }
        });
      }
    });
  </script>
</body>

</html>