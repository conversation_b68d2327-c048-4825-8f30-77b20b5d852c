<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>宝宝日常护理新手指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }



    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }


  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">宝宝日常护理</h1>
        <p class="text-sm mt-2 text-white/80">新手爸妈必备指南</p>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">

      <!-- 沐浴与清洁 -->
      <section id="bath_care" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">沐浴与清洁</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">🛁 洗澡其实很简单</h3>
          <p class="text-lg">别紧张！给宝宝洗澡没有想象中那么难。掌握基本要领，注意安全，你和宝宝都会越来越享受这个过程。</p>
        </div>

        <!-- 1. 基础洗澡方法 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">1. 新生儿洗澡步骤</h3>
          
          <div class="warning-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">⚠️ 重要提醒</h4>
            <p class="text-lg mb-4">新生儿脐带未脱落前，<strong>不能盆浴</strong>！只能海绵擦身。脐带脱落后才可以泡澡。</p>
          </div>

          <h4 class="text-xl font-bold text-[#A57C4F] mb-4">新手爸妈洗澡五步法</h4>
          
          <div class="mb-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">1</div>
                <h6 class="font-bold text-sm mb-1">准备齐全</h6>
                <p class="text-xs text-gray-600">房间温暖(25-28℃)，毛巾、换洗衣物都准备好</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">2</div>
                <h6 class="font-bold text-sm mb-1">温水测试</h6>
                <p class="text-xs text-gray-600">用手腕内侧测水温，感觉温热不烫手</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">3</div>
                <h6 class="font-bold text-sm mb-1">从脸开始</h6>
                <p class="text-xs text-gray-600">眼睛从内向外擦，先洗脸再洗头</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">4</div>
                <h6 class="font-bold text-sm mb-1">身体清洁</h6>
                <p class="text-xs text-gray-600">从上到下，注意褶皱处，最后洗屁屁</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">5</div>
                <h6 class="font-bold text-sm mb-1">快速包裹</h6>
                <p class="text-xs text-gray-600">洗完立即用浴巾包好，防止着凉</p>
              </div>
            </div>
          </div>

          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h5 class="font-bold text-blue-800 mb-2">💡 新手爸妈小贴士：</h5>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• 第一次可以夫妻配合，一个抱宝宝一个洗</li>
              <li>• 宝宝哭闹是正常的，温柔说话安抚</li>
              <li>• 动作轻柔但要有信心，宝宝能感受到</li>
              <li>• 脐带护理用酒精，从里往外擦拭</li>
            </ul>
          </div>
        </div>

        <!-- 2. 日常护理细节 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">2. 日常护理细节</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 class="text-xl font-bold mb-4">指甲护理</h4>
              <ul class="space-y-3 text-gray-700">
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>宝宝睡觉时修剪最安全</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>用婴儿专用指甲钳，沿弧度剪</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>不要剪太短，留一点白边</li>
                <li class="text-red-600 font-bold">❌ 不要用成人指甲钳</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold mb-4">耳鼻清洁</h4>
              <div class="space-y-3">
                <div class="p-3 bg-gray-50 rounded-lg">
                  <h5 class="font-bold text-sm">👂 耳朵</h5>
                  <p class="text-xs text-gray-600">只擦外耳廓，绝不能用棉签掏耳道</p>
                </div>
                <div class="p-3 bg-gray-50 rounded-lg">
                  <h5 class="font-bold text-sm">👃 鼻子</h5>
                  <p class="text-xs text-gray-600">用生理盐水软化后轻轻吸出</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 穿衣搭配 -->
      <section id="clothing_guide" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">穿衣搭配</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">👕 穿衣原则很简单</h3>
          <p class="text-lg">记住"洋葱穿衣法"：比大人多一层！摸宝宝后颈判断冷热，温热干爽就对了。</p>
        </div>

        <div class="space-y-6">
          <h3 class="text-2xl font-bold text-[#A57C4F]">季节穿衣指南</h3>
          
          <!-- 夏季穿衣 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">☀️ 夏季 (26-28℃)</h4>
            <p class="text-gray-600 mb-4">单层轻薄就够了！纯棉透气最重要，空调房里可以加个小肚兜。</p>
            <div class="flex items-center space-x-2 flex-wrap">
              <span class="bg-[#A57C4F] text-white px-4 py-2 rounded-full text-sm font-medium">包屁衣或连体衣</span>
              <span class="text-[#A57C4F] font-bold">+</span>
              <span class="bg-[#EACDA4] text-[#434242] px-4 py-2 rounded-full text-sm font-medium">轻薄睡袋（空调房）</span>
            </div>
          </div>

          <!-- 春秋季穿衣 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">🍂 春秋季 (20-25℃)</h4>
            <p class="text-gray-600 mb-4">最适合"洋葱穿衣法"！内衣打底，外面加一层，热了脱冷了穿。</p>
            <div class="flex items-center space-x-2 flex-wrap">
              <span class="bg-[#A57C4F] text-white px-4 py-2 rounded-full text-sm font-medium">贴身包屁衣</span>
              <span class="text-[#A57C4F] font-bold">+</span>
              <span class="bg-[#A57C4F] text-white px-4 py-2 rounded-full text-sm font-medium">长袖上衣长裤</span>
              <span class="text-[#A57C4F] font-bold">+</span>
              <span class="bg-[#EACDA4] text-[#434242] px-4 py-2 rounded-full text-sm font-medium">薄外套（备用）</span>
            </div>
          </div>

          <!-- 冬季穿衣 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">❄️ 冬季 (<20℃)</h4>
            <p class="text-gray-600 mb-4">保暖但别捂太严！内衣+保暖衣+睡袋，记住宝宝比大人怕热。</p>
            <div class="flex items-center space-x-2 flex-wrap">
              <span class="bg-[#A57C4F] text-white px-4 py-2 rounded-full text-sm font-medium">贴身内衣</span>
              <span class="text-[#A57C4F] font-bold">+</span>
              <span class="bg-[#A57C4F] text-white px-4 py-2 rounded-full text-sm font-medium">保暖连体衣</span>
              <span class="text-[#A57C4F] font-bold">+</span>
              <span class="bg-[#A57C4F] text-white px-4 py-2 rounded-full text-sm font-medium">厚睡袋</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 用品清单 -->
      <section id="baby_essentials" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">用品清单</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">📋 买对不买贵</h3>
          <p class="text-lg">新手爸妈最容易买一堆没用的。这份清单帮你分清"必需品"和"可选品"，省钱又实用。</p>
        </div>

        <!-- 洗护用品 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🧴 洗护用品</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">婴儿沐浴露（温和无泪配方）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">柔软浴巾（带帽子最好）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span class="text-gray-700">婴儿洗发水</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-blue-500 text-white">推荐</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">婴儿润肤霜</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">婴儿指甲钳</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span class="text-gray-700">软毛牙刷（6个月后）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-blue-500 text-white">推荐</span>
            </div>
          </div>
        </div>

        <!-- 衣物用品 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">👕 衣物用品</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">纯棉包屁衣（多准备几件）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">连体衣</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">睡袋（代替被子更安全）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span class="text-gray-700">小帽子</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-blue-500 text-white">推荐</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">袜子</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
          </div>
        </div>

        <!-- 日用品 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🍼 日用品</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">纸尿裤</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">湿纸巾（无香料）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">护臀膏</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">体温计</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
          </div>
        </div>

        <!-- 安全用品 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🛡️ 安全用品</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">汽车安全座椅</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <span class="text-gray-700">婴儿床（符合安全标准）</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-red-500 text-white">必需</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span class="text-gray-700">婴儿监视器</span>
              <span class="text-xs font-semibold px-2 py-1 rounded-full bg-blue-500 text-white">推荐</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 常见问题 -->
      <section id="common_issues" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">常见问题</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">🛠️ 别慌，都有解决办法</h3>
          <p class="text-lg">新手爸妈遇到问题很正常！这里整理了最常见的护理问题和解决方案。</p>
        </div>

        <!-- 问题卡片网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">宝宝不爱洗澡一直哭怎么办？</h4>
            <div class="space-y-4">
              <div>
                <h5 class="font-bold text-gray-800 mb-2">原因分析：</h5>
                <ul class="text-gray-700 space-y-1">
                  <li>• 水温不合适（太热或太凉）</li>
                  <li>• 房间温度低，宝宝觉得冷</li>
                  <li>• 动作太急，宝宝没安全感</li>
                </ul>
              </div>
              <div>
                <h5 class="font-bold text-green-800 mb-2">解决方法：</h5>
                <ul class="text-gray-700 space-y-1">
                  <li>• 洗澡前先暖房间，水温37-40°C</li>
                  <li>• 轻声说话安抚，动作温柔</li>
                  <li>• 可以放轻音乐营造氛围</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">宝宝皮肤出现红疹？</h4>
            <div class="space-y-4">
              <div>
                <h5 class="font-bold text-gray-800 mb-2">常见类型：</h5>
                <ul class="text-gray-700 space-y-1">
                  <li>• <strong>湿疹：</strong>红斑、小水泡，可能是过敏</li>
                  <li>• <strong>痱子：</strong>小红点，通常是太热引起</li>
                  <li>• <strong>尿布疹：</strong>屁屁红肿，换尿布不及时</li>
                </ul>
              </div>
              <div>
                <h5 class="font-bold text-green-800 mb-2">处理方法：</h5>
                <ul class="text-gray-700 space-y-1">
                  <li>• 保持皮肤清洁干燥</li>
                  <li>• 湿疹可用婴儿润肤霜</li>
                  <li>• 严重时及时看医生</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">怎么判断宝宝冷热？</h4>
            <div class="space-y-4">
              <div>
                <h5 class="font-bold text-gray-800 mb-2">最准确的方法：</h5>
                <ul class="text-gray-700 space-y-1">
                  <li>• <strong>摸后颈：</strong>温热干爽最合适</li>
                  <li>• <strong>摸手脚：</strong>稍微凉一点是正常的</li>
                </ul>
              </div>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div class="p-3 bg-red-50 rounded-lg">
                  <h6 class="font-bold text-red-800 text-sm mb-1">穿太多信号：</h6>
                  <ul class="text-xs text-red-700 space-y-1">
                    <li>• 后颈出汗潮湿</li>
                    <li>• 脸蛋通红</li>
                    <li>• 烦躁不安</li>
                  </ul>
                </div>
                <div class="p-3 bg-blue-50 rounded-lg">
                  <h6 class="font-bold text-blue-800 text-sm mb-1">穿太少信号：</h6>
                  <ul class="text-xs text-blue-700 space-y-1">
                    <li>• 后颈发凉</li>
                    <li>• 手脚冰凉</li>
                    <li>• 哭闹不止</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold text-[#A57C4F] mb-4">什么时候需要看医生？</h4>
            <div class="space-y-4">
              <div class="warning-box p-4 rounded-lg">
                <h5 class="font-bold text-red-800 mb-2">⚠️ 立即就医：</h5>
                <ul class="text-red-700 space-y-1">
                  <li>• 发热超过38°C</li>
                  <li>• 皮疹伴有发热</li>
                  <li>• 呼吸困难或急促</li>
                  <li>• 严重腹泻或呕吐</li>
                  <li>• 哭声异常（尖锐或微弱）</li>
                </ul>
              </div>
              <div class="highlight-box p-4 rounded-lg">
                <h5 class="font-bold text-green-800 mb-2">💡 可预约咨询：</h5>
                <ul class="text-green-700 space-y-1">
                  <li>• 轻微皮疹持续不消</li>
                  <li>• 睡眠或饮食习惯突然改变</li>
                  <li>• 任何让你担心的异常情况</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

    </main>
  </div>

  <footer class="text-center py-8 mt-16 border-t border-gray-200">
    <p class="text-gray-500">愿每个宝宝都健康快乐成长！</p>
  </footer>

  <script>
    const sections = [
      { id: 'bath_care', title: '沐浴清洁', icon: '🛁' },
      { id: 'clothing_guide', title: '穿衣搭配', icon: '👕' },
      { id: 'baby_essentials', title: '用品清单', icon: '📋' },
      { id: 'common_issues', title: '常见问题', icon: '🛠️' }
    ];

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

                 window.scrollTo(0, 0);
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1);
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        activateSection('bath_care');
      }

      window.addEventListener('hashchange', handleHashChange);

      
    });
  </script>
</body>

</html>