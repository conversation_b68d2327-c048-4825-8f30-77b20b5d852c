<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>过敏与食欲</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }

    .accordion-button.open .accordion-arrow {
      transform: rotate(180deg);
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }

    .chart-container {
      position: relative;
      width: 100%;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      height: 300px;
    }

    @media (min-width: 640px) {
      .chart-container {
        height: 350px;
      }
    }

    .allergen-card {
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .allergen-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .modal {
      display: none;
    }

    .modal.active {
      display: flex;
    }

    .flowchart-item {
      border: 2px solid #D4E2D4;
      background-color: #F0F5F0;
    }

    .flowchart-arrow {
      color: #77B0AA;
    }
  </style>
</head>

<body>
  <div id="modal-container" class="modal fixed inset-0 bg-black bg-opacity-50 justify-center items-center z-50">
    <div
      class="bg-white rounded-lg shadow-xl p-6 w-11/12 md:w-1/2 max-w-2xl transform transition-all duration-300 ease-out">
      <div class="flex justify-between items-center">
        <h3 id="modal-title" class="text-2xl font-bold text-[#434242]"></h3>
        <button id="modal-close-btn" class="text-gray-500 hover:text-gray-800 text-3xl">&times;</button>
      </div>
      <div id="modal-content" class="mt-4 text-gray-600 space-y-2"></div>
    </div>
  </div>

  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">过敏与食欲</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 宝宝过敏 -->
      <section id="baby_allergy" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">宝宝过敏</h2>

        <!-- 症状速查 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🔍 过敏信号</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">📋 症状快速自查</h4>
            <p class="text-lg mb-4">根据宝宝的症状，快速了解可能的过敏类型。<strong>这不能替代医生诊断</strong>，但可以帮您做初步判断。</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🍼 食物过敏信号</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>呕吐、腹泻、便血</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>喂养困难、拒食</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>嘴唇、面部肿胀</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>皮疹、湿疹加重</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>异常哭闹、烦躁</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🫁 呼吸道过敏信号</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>持续咳嗽、喘息</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>打喷嚏、流鼻涕</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>鼻塞、揉鼻子</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>眼睛红痒、流泪</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>夜间咳嗽加重</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🧴 皮肤过敏信号</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-green-500 mr-2">●</span>风团、皮疹、湿疹</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">●</span>皮肤瘙痒、干燥</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">●</span>反复搔抓</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">●</span>皮肤增厚、粗糙</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">●</span>睡眠不安</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">💊 药物过敏信号</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-purple-500 mr-2">●</span>用药后突发皮疹</li>
                <li class="flex items-start"><span class="text-purple-500 mr-2">●</span>荨麻疹（风团）</li>
                <li class="flex items-start"><span class="text-purple-500 mr-2">●</span>呼吸困难</li>
                <li class="flex items-start"><span class="text-purple-500 mr-2">●</span>面部、嘴唇肿胀</li>
                <li class="flex items-start"><span class="text-purple-500 mr-2">●</span>疫苗接种后反应</li>
              </ul>
            </div>
          </div>

          <div class="warning-box p-4 rounded-lg mt-6">
            <h5 class="font-bold text-red-800 mb-2">🚨 紧急就医信号</h5>
            <p class="text-sm text-red-700">如果宝宝出现呼吸困难、面部肿胀、全身荨麻疹、反复呕吐等严重症状，请立即就医！</p>
          </div>
        </div>

        <!-- 常见过敏原 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🌟 常见过敏原分类</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">📊 全面了解过敏原</h4>
            <p class="text-lg mb-4">过敏原不仅包括食物，还有环境中的各种物质。<strong>全方位防护</strong>才能更好地保护宝宝。</p>
          </div>

          <!-- 食物过敏原 -->
          <div class="mb-8">
            <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🍽️ 九大食物过敏原</h4>
            <p class="text-gray-600 mb-4">约90%的食物过敏由以下9种食物引起</p>

            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 gap-4 mb-6">
              <div class="allergen-card cursor-pointer bg-amber-50 p-4 rounded-lg text-center" data-allergen="milk">
                <div class="text-4xl">🥛</div>
                <div class="font-semibold mt-2">牛奶</div>
                <div class="text-xs text-gray-500">婴儿最常见</div>
              </div>
              <div class="allergen-card cursor-pointer bg-yellow-50 p-4 rounded-lg text-center" data-allergen="egg">
                <div class="text-4xl">🥚</div>
                <div class="font-semibold mt-2">鸡蛋</div>
                <div class="text-xs text-gray-500">蛋清更易过敏</div>
              </div>
              <div class="allergen-card cursor-pointer bg-orange-100 p-4 rounded-lg text-center" data-allergen="peanut">
                <div class="text-4xl">🥜</div>
                <div class="font-semibold mt-2">花生</div>
                <div class="text-xs text-gray-500">可能终身过敏</div>
              </div>
              <div class="allergen-card cursor-pointer bg-lime-50 p-4 rounded-lg text-center" data-allergen="treenut">
                <div class="text-4xl">🌰</div>
                <div class="font-semibold mt-2">木本坚果</div>
                <div class="text-xs text-gray-500">核桃、杏仁等</div>
              </div>
              <div class="allergen-card cursor-pointer bg-green-50 p-4 rounded-lg text-center" data-allergen="soy">
                <div class="text-4xl">🌱</div>
                <div class="font-semibold mt-2">大豆</div>
                <div class="text-xs text-gray-500">多数可耐受</div>
              </div>
              <div class="allergen-card cursor-pointer bg-amber-100 p-4 rounded-lg text-center" data-allergen="wheat">
                <div class="text-4xl">🌾</div>
                <div class="font-semibold mt-2">小麦</div>
                <div class="text-xs text-gray-500">面食、面包</div>
              </div>
              <div class="allergen-card cursor-pointer bg-sky-50 p-4 rounded-lg text-center" data-allergen="fish">
                <div class="text-4xl">🐟</div>
                <div class="font-semibold mt-2">鱼类</div>
                <div class="text-xs text-gray-500">通常终身过敏</div>
              </div>
              <div class="allergen-card cursor-pointer bg-red-50 p-4 rounded-lg text-center" data-allergen="shellfish">
                <div class="text-4xl">🦐</div>
                <div class="font-semibold mt-2">甲壳类</div>
                <div class="text-xs text-gray-500">虾、蟹、龙虾</div>
              </div>
              <div class="allergen-card cursor-pointer bg-stone-100 p-4 rounded-lg text-center" data-allergen="sesame">
                <div class="text-4xl">⚫</div>
                <div class="font-semibold mt-2">芝麻</div>
                <div class="text-xs text-gray-500">隐藏在烘焙品</div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="p-4 border-2 border-green-200 rounded-lg bg-green-50">
                  <h5 class="font-bold text-green-800 mb-2">✅ 婴儿期容易耐受的</h5>
                  <ul class="text-sm text-green-700 space-y-1">
                    <li>• 牛奶过敏：3-5岁后多数耐受</li>
                    <li>• 鸡蛋过敏：学龄期多数耐受</li>
                    <li>• 大豆过敏：随成长常能耐受</li>
                    <li>• 小麦过敏：部分可耐受</li>
                  </ul>
                </div>
                <div class="p-4 border-2 border-red-200 rounded-lg bg-red-50">
                  <h5 class="font-bold text-red-800 mb-2">⚠️ 通常终身过敏的</h5>
                  <ul class="text-sm text-red-700 space-y-1">
                    <li>• 花生：严重反应风险高</li>
                    <li>• 木本坚果：需终身避免</li>
                    <li>• 鱼类和甲壳类：成人后仍过敏</li>
                    <li>• 芝麻：新兴的主要过敏原</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 环境过敏原 -->
            <div class="mb-8">
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🌿 常见环境过敏原</h4>
              <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-green-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🏠</div>
                  <div class="font-semibold mt-2">尘螨</div>
                  <div class="text-xs text-gray-500">床品、地毯</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🌸</div>
                  <div class="font-semibold mt-2">花粉</div>
                  <div class="text-xs text-gray-500">树木、草本</div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🍄</div>
                  <div class="font-semibold mt-2">霉菌</div>
                  <div class="text-xs text-gray-500">潮湿环境</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🐱</div>
                  <div class="font-semibold mt-2">宠物毛屑</div>
                  <div class="text-xs text-gray-500">猫、狗毛发</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🦗</div>
                  <div class="font-semibold mt-2">昆虫</div>
                  <div class="text-xs text-gray-500">蟑螂、蚊虫</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">💨</div>
                  <div class="font-semibold mt-2">空气污染</div>
                  <div class="text-xs text-gray-500">烟雾、化学物</div>
                </div>
                <div class="bg-pink-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🧴</div>
                  <div class="font-semibold mt-2">化妆品</div>
                  <div class="text-xs text-gray-500">香精、防腐剂</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg text-center">
                  <div class="text-3xl">🧸</div>
                  <div class="font-semibold mt-2">纺织品</div>
                  <div class="text-xs text-gray-500">羊毛、合成纤维</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 过敏防护策略 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🛡️ 过敏防护策略</h3>

            <div class="highlight-box p-6 rounded-lg mb-6">
              <h4 class="text-xl font-bold mb-4">🎯 预防胜于治疗</h4>
              <p class="text-lg">有效的过敏防护需要<strong>多方面协同</strong>，从环境控制到生活习惯，全方位保护宝宝远离过敏原。</p>
            </div>

            <!-- 环境防护 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-6">
              <div class="bg-blue-50 p-6 rounded-lg">
                <h4 class="text-xl font-bold text-blue-800 mb-4 text-center">🏠 居家环境防护</h4>
                <div class="space-y-4">
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"><span>使用防螨床品包裹床垫枕头</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"><span>每周用>55℃热水清洗床品</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"><span>移走地毯和过多毛绒玩具</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"><span>湿度控制在40-50%</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"><span>定期清洁空调滤网</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"><span>使用空气净化器</span></label>
                </div>
              </div>

              <div class="bg-green-50 p-6 rounded-lg">
                <h4 class="text-xl font-bold text-green-800 mb-4 text-center">🌸 季节性防护</h4>
                <div class="space-y-4">
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500"><span>花粉季节紧闭门窗</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500"><span>外出戴口罩、护目镜</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500"><span>回家后立即洗手洗脸</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500"><span>避免早晨和傍晚外出</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500"><span>雨后外出降低花粉接触</span></label>
                  <label class="flex items-center space-x-3"><input type="checkbox"
                      class="h-5 w-5 rounded border-gray-300 text-green-600 focus:ring-green-500"><span>选择花粉浓度低的地区活动</span></label>
                </div>
              </div>
            </div>

            <!-- 具体防护措施 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="p-4 border-2 border-yellow-200 rounded-lg bg-yellow-50">
                <h5 class="font-bold text-yellow-800 mb-2">🧴 皮肤防护</h5>
                <ul class="text-sm text-yellow-700 space-y-1">
                  <li>• 每天温水沐浴5-10分钟</li>
                  <li>• 3分钟内涂抹厚重保湿霜</li>
                  <li>• 选择无香料、无刺激产品</li>
                  <li>• 避免过度清洁和搓洗</li>
                  <li>• 穿着纯棉、透气衣物</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
                <h5 class="font-bold text-blue-800 mb-2">🫁 呼吸道防护</h5>
                <ul class="text-sm text-blue-700 space-y-1">
                  <li>• 识别和避免触发因素</li>
                  <li>• 保持室内空气清新</li>
                  <li>• 避免接触二手烟</li>
                  <li>• 控制强烈气味刺激</li>
                  <li>• 及时治疗呼吸道感染</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-purple-200 rounded-lg bg-purple-50">
                <h5 class="font-bold text-purple-800 mb-2">🍽️ 饮食防护</h5>
                <ul class="text-sm text-purple-700 space-y-1">
                  <li>• 仔细阅读食品标签</li>
                  <li>• 避免交叉污染</li>
                  <li>• 外出就餐提前告知过敏史</li>
                  <li>• 准备安全的替代食物</li>
                  <li>• 携带过敏急救药物</li>
                </ul>
              </div>
            </div>
          </div>
      </section>

      <!-- 食欲提升 -->
      <section id="appetite_improvement" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">宝宝食欲提升</h2>

        <!-- 食欲不振的原因 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🤔 宝宝为什么不爱吃饭？</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">🔍 多重因素分析</h4>
            <p class="text-lg mb-4">宝宝食欲不振往往是<strong>多种因素综合作用</strong>的结果。了解根本原因，才能对症下药。</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🏥 生理因素</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>疾病期间：感冒、发烧、消化不良</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>口腔问题：出牙期不适、口腔溃疡</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>消化系统：便秘、腹胀、胃肠功能弱</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>营养缺乏：缺铁、缺锌影响味觉</li>
                <li class="flex items-start"><span class="text-red-500 mr-2">●</span>药物影响：某些药物抑制食欲</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">🧠 心理行为因素</h4>
              <ul class="space-y-2 text-gray-700">
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>环境因素：嘈杂、分散注意力</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>喂养方式：强迫进食、追着喂</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>情绪状态：焦虑、紧张、不安</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>饮食习惯：零食过多、不规律</li>
                <li class="flex items-start"><span class="text-blue-500 mr-2">●</span>发育阶段：正常的生长减缓期</li>
              </ul>
            </div>
          </div>

          <div class="warning-box p-4 rounded-lg mt-6">
            <h5 class="font-bold text-red-800 mb-2">⚠️ 需要就医的情况</h5>
            <p class="text-sm text-red-700">持续2周以上食欲不振、体重明显下降、伴有发热呕吐等症状时，应及时就医检查。</p>
          </div>
        </div>

        <!-- 提升食欲的方法 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🍽️ 提升食欲实用方法</h3>

          <!-- 核心方法 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="p-4 border-2 border-green-200 rounded-lg bg-green-50">
              <h5 class="font-bold text-green-800 mb-3 text-lg">✅ 这样做有效</h5>
              <ul class="text-sm text-green-700 space-y-2">
                <li><strong>固定时间地点：</strong>每餐定时定点，关闭电视</li>
                <li><strong>让宝宝参与：</strong>一起准备食物，增加兴趣</li>
                <li><strong>食物要诱人：</strong>色彩丰富，造型有趣</li>
                <li><strong>餐前饿一下：</strong>饭前1小时停止零食</li>
                <li><strong>全家一起吃：</strong>做好榜样，营造氛围</li>
              </ul>
            </div>

            <div class="p-4 border-2 border-red-200 rounded-lg bg-red-50">
              <h5 class="font-bold text-red-800 mb-3 text-lg">🚫 这样做无效</h5>
              <ul class="text-sm text-red-700 space-y-2">
                <li><strong>强迫进食：</strong>威胁、哄骗只会更抗拒</li>
                <li><strong>追着喂饭：</strong>破坏用餐规矩</li>
                <li><strong>零食当奖励：</strong>影响正餐食欲</li>
                <li><strong>餐桌上批评：</strong>造成负面情绪</li>
                <li><strong>过分关注食量：</strong>增加压力</li>
              </ul>
            </div>
          </div>

          <div class="warning-box p-4 rounded-lg">
            <h5 class="font-bold text-red-800 mb-2">⚠️ 何时需要关注</h5>
            <p class="text-sm text-red-700">持续2周以上食欲明显下降，体重不增长或下降时应咨询医生。</p>
          </div>
        </div>
      </section>

      <!-- 常见问题 -->
      <section id="other_problems" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">常见问题</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">💡 常见疑问解答</h3>
          <p class="text-lg">以下是家长们经常遇到的问题和专业建议，帮助您<strong>科学应对各种育儿挑战</strong>。</p>
        </div>



        <!-- 常见问题解答 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">❓ 常见问题解答</h3>

          <div id="accordion-container" class="space-y-3">
            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button
                class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>宝宝过敏了还能打疫苗吗？</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                  <strong>大多数情况下可以：</strong><br>
                  • 食物过敏不影响疫苗接种<br>
                  • 鸡蛋过敏儿童通常也能安全接种麻腮风和流感疫苗<br>
                  • 湿疹、哮喘等慢性过敏性疾病不是疫苗禁忌<br>
                  <strong class="text-red-600">重要提醒：</strong><br>
                  • 接种前必须告知医生详细过敏史<br>
                  • 严重过敏反应史需要专科医生评估<br>
                  • 接种后在医院观察30分钟<br>
                  • 携带急救药物备用
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button
                class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>如何区分过敏性鼻炎和感冒？</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                  <strong>过敏性鼻炎的特征：</strong><br>
                  • 症状持续2周以上，反复发作<br>
                  • 接触特定过敏原后加重<br>
                  • 鼻涕清澈如水，不发热<br>
                  • 鼻痒、眼痒，频繁打喷嚏<br>
                  • 季节性发作或环境相关<br>
                  • 晨起症状明显<br>
                  <strong>普通感冒的特征：</strong><br>
                  • 症状7-10天内逐渐好转<br>
                  • 可能伴有发热、全身不适<br>
                  • 鼻涕由清澈变为黄绿色<br>
                  • 咽痛、咳嗽较明显
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button
                class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>宝宝不爱吃饭，家长应该怎么做？</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                  <strong>正确的应对方式：</strong><br>
                  • 保持冷静，不要焦虑<br>
                  • 相信孩子的饥饱感<br>
                  • 定时定点提供营养丰富的食物<br>
                  • 营造愉快的用餐氛围<br>
                  • 成为孩子的好榜样<br>
                  <strong>错误的做法：</strong><br>
                  • 强迫进食或威胁惩罚<br>
                  • 追着孩子喂饭<br>
                  • 用零食哄骗或替代正餐<br>
                  • 在餐桌上批评指责<br>
                  <strong>何时需要关注：</strong><br>
                  持续2周以上食欲明显下降，体重不增长或下降时应咨询医生
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button
                class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>什么时候需要看过敏科医生？</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                  <strong class="text-red-600">紧急就医情况：</strong><br>
                  • 严重过敏反应（血管性水肿、呼吸困难）<br>
                  • 全身荨麻疹伴有呼吸症状<br>
                  • 反复呕吐、腹泻伴脱水<br>
                  <strong>建议专科咨询：</strong><br>
                  • 反复湿疹，常规治疗效果差<br>
                  • 怀疑多种食物或环境过敏<br>
                  • 需要进行过敏原检测<br>
                  • 考虑脱敏治疗<br>
                  • 过敏严重影响生活质量<br>
                  <strong>就诊准备：</strong><br>
                  详细记录过敏症状、发作时间、可能诱因，拍照记录皮疹等表现
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button
                class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>如何为过敏宝宝选择特殊配方奶粉？</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                  <strong>配方选择原则：</strong><br>
                  • <strong>轻度过敏风险：</strong>部分水解配方（预防作用）<br>
                  • <strong>轻度牛奶过敏：</strong>深度水解配方<br>
                  • <strong>严重牛奶过敏：</strong>氨基酸配方<br>
                  • <strong>大豆过敏风险：</strong>避免豆奶配方<br>
                  <strong>注意事项：</strong><br>
                  • 必须在医生指导下选择<br>
                  • 观察2-4周评估效果<br>
                  • 不要频繁更换品牌<br>
                  • 定期监测生长发育<br>
                  • 6个月以下婴儿不建议豆奶配方<br>
                  <strong>转奶建议：</strong><br>
                  逐步替换，观察宝宝接受度和过敏症状改善情况
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button
                class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>宝宝湿疹反复发作怎么办？</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                  <strong>日常护理要点：</strong><br>
                  • 每天温水沐浴5-10分钟<br>
                  • 沐浴后3分钟内涂抹保湿霜<br>
                  • 选择无香料、低敏的护肤品<br>
                  • 穿着纯棉、宽松的衣物<br>
                  • 保持室内湿度40-60%<br>
                  <strong>避免诱发因素：</strong><br>
                  • 识别并避免过敏原<br>
                  • 避免过度清洁和摩擦<br>
                  • 控制情绪压力<br>
                  • 及时治疗皮肤感染<br>
                  <strong>用药原则：</strong><br>
                  • 遵医嘱使用外用药物<br>
                  • 不要害怕合理使用激素类药膏<br>
                  • 症状控制后逐步减量<br>
                  • 长期使用润肤剂维护
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <footer class="text-center py-6 mt-8 border-t-2 border-gray-200">
    <p class="text-gray-500 text-sm">本指南信息仅供参考，不能替代专业医疗建议、诊断或治疗。如有任何医疗问题，请务必咨询您的医生或其他合格的医疗服务提供者。</p>
  </footer>

  <script>
    const sections = [
      { id: 'baby_allergy', title: '宝宝过敏', icon: '🤧' },
      { id: 'appetite_improvement', title: '食欲提升', icon: '🍽️' },
      { id: 'other_problems', title: '常见问题', icon: '❓' }
    ];

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');
      let accordionInitialized = false;

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

        window.scrollTo(0, 0);

        if (targetId === 'other_problems') {
          setupAccordion();
        }
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1); // 去掉#符号
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        // 默认激活第一个标签
        activateSection('baby_allergy');
      }

      // 监听hash变化（支持浏览器前进后退）
      window.addEventListener('hashchange', handleHashChange);

      // 过敏原模态框功能
      const modal = document.getElementById('modal-container');
      const modalTitle = document.getElementById('modal-title');
      const modalContent = document.getElementById('modal-content');
      const modalCloseBtn = document.getElementById('modal-close-btn');

      const allergenData = {
        milk: {
          title: '牛奶过敏',
          content: [
            '🎯 <strong>症状特征：</strong>呕吐、腹泻、湿疹、哭闹不安、体重增长缓慢',
            '⏰ <strong>发生时间：</strong>通常在出生后几周到几个月内出现',
            '🔄 <strong>预后情况：</strong>大部分孩子在3-5岁时能够耐受牛奶',
            '🍼 <strong>喂养建议：</strong>深度水解配方或氨基酸配方奶粉',
            '⚠️ <strong>注意事项：</strong>避免所有奶制品，包括奶油、奶酪、酸奶等'
          ]
        },
        egg: {
          title: '鸡蛋过敏',
          content: [
            '🎯 <strong>症状特征：</strong>皮疹、湿疹、消化不良、呼吸道症状',
            '🥚 <strong>过敏部位：</strong>蛋清比蛋黄更容易引起过敏',
            '🔄 <strong>预后情况：</strong>多数儿童在学龄期能够耐受鸡蛋',
            '🍳 <strong>替代选择：</strong>鸭蛋、鹌鹑蛋也可能过敏，需谨慎尝试',
            '💉 <strong>疫苗考虑：</strong>严重鸡蛋过敏需要在接种含鸡蛋的疫苗前评估'
          ]
        },
        peanut: {
          title: '花生过敏',
          content: [
            '🎯 <strong>症状特征：</strong>可能引起严重的全身过敏反应',
            '⚡ <strong>反应速度：</strong>通常在接触后几分钟到1小时内出现',
            '🔄 <strong>预后情况：</strong>大多数为终身过敏，很少能够耐受',
            '🏠 <strong>环境控制：</strong>家中彻底清除花生及其制品',
            '🚨 <strong>急救准备：</strong>严重过敏者需随身携带肾上腺素自动注射器'
          ]
        },
        treenut: {
          title: '木本坚果过敏',
          content: [
            '🌰 <strong>包括种类：</strong>杏仁、核桃、腰果、开心果、榛子等',
            '🎯 <strong>症状特征：</strong>从轻微皮疹到严重全身反应',
            '🔄 <strong>预后情况：</strong>通常为终身过敏',
            '🔍 <strong>交叉反应：</strong>对一种坚果过敏可能对其他坚果也过敏',
            '📖 <strong>标签阅读：</strong>注意"可能含有坚果"的食品标识'
          ]
        },
        soy: {
          title: '大豆过敏',
          content: [
            '🎯 <strong>症状特征：</strong>通常症状较轻，主要是消化道症状',
            '👶 <strong>年龄特点：</strong>多见于婴幼儿，成人较少',
            '🔄 <strong>预后情况：</strong>大多数儿童随年龄增长能够耐受',
            '🥛 <strong>豆奶考虑：</strong>豆奶配方不适合6个月以下牛奶过敏的婴儿',
            '🍜 <strong>隐藏来源：</strong>酱油、豆腐、味增等都含有大豆'
          ]
        },
        wheat: {
          title: '小麦过敏',
          content: [
            '🎯 <strong>症状特征：</strong>消化道症状、皮肤症状、呼吸道症状',
            '🍞 <strong>食物来源：</strong>面包、面条、饼干、蛋糕等',
            '🔄 <strong>预后情况：</strong>部分儿童能够随年龄增长耐受',
            '🌾 <strong>替代选择：</strong>大米、燕麦、玉米、薯类',
            '⚠️ <strong>注意区别：</strong>小麦过敏不等同于乳糜泻（麸质过敏）'
          ]
        },
        fish: {
          title: '鱼类过敏',
          content: [
            '🎯 <strong>症状特征：</strong>可引起严重过敏反应',
            '🐟 <strong>种类特异：</strong>对某种鱼过敏不代表对所有鱼都过敏',
            '🔄 <strong>预后情况：</strong>通常为终身过敏',
            '💨 <strong>接触方式：</strong>烹饪时的蒸汽也可能引起反应',
            '🐠 <strong>营养替代：</strong>其他蛋白质来源如肉类、豆类等'
          ]
        },
        shellfish: {
          title: '甲壳类过敏',
          content: [
            '🦐 <strong>包括种类：</strong>虾、蟹、龙虾、贝类等',
            '🎯 <strong>症状特征：</strong>常引起严重过敏反应',
            '🔄 <strong>预后情况：</strong>通常为终身过敏',
            '👨‍⚕️ <strong>成人发病：</strong>也是成人最常见的食物过敏之一',
            '🍽️ <strong>交叉污染：</strong>注意海鲜餐厅的交叉污染风险'
          ]
        },
        sesame: {
          title: '芝麻过敏',
          content: [
            '🎯 <strong>症状特征：</strong>可引起从轻微到严重的各种反应',
            '🔄 <strong>预后情况：</strong>通常持续到成年',
            '🍞 <strong>隐藏来源：</strong>面包、饼干、沙拉酱、调料等',
            '🌱 <strong>其他形式：</strong>芝麻油、芝麻酱、香油等',
            '📖 <strong>标签识别：</strong>注意tahini（芝麻酱）等外文标识'
          ]
        }
      };

      // 过敏原卡片点击事件
      document.querySelectorAll('.allergen-card').forEach(card => {
        card.addEventListener('click', function () {
          const allergen = this.dataset.allergen;
          const data = allergenData[allergen];

          if (data) {
            modalTitle.textContent = data.title;
            modalContent.innerHTML = data.content.map(item => `<p class="mb-2">${item}</p>`).join('');
            modal.classList.add('active');
          }
        });
      });

      // 关闭模态框
      modalCloseBtn.addEventListener('click', function () {
        modal.classList.remove('active');
      });

      modal.addEventListener('click', function (e) {
        if (e.target === modal) {
          modal.classList.remove('active');
        }
      });



      // 手风琴功能
      function setupAccordion() {
        const accordionContainer = document.getElementById('accordion-container');
        if (!accordionContainer || accordionInitialized) return;
        accordionInitialized = true;

        accordionContainer.addEventListener('click', function (e) {
          const button = e.target.closest('.accordion-button');
          if (button) {
            button.classList.toggle('open');
            const content = button.nextElementSibling;
            if (content.style.maxHeight) {
              content.style.maxHeight = null;
            } else {
              content.style.maxHeight = content.scrollHeight + "px";
            }
          }
        });
      }
    });
  </script>
</body>

</html>