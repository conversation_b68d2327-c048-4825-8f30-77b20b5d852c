<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>辅食喂养新手指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }

    .accordion-button.open .accordion-arrow {
      transform: rotate(180deg);
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }
    
    .chart-container {
      position: relative;
      width: 100%;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
      height: 350px;
      max-height: 50vh;
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }
  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">辅食喂养</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- Tab 1: 辅食制作 -->
      <section id="tab1" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">辅食制作</h2>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">1. 好的工具是成功的一半</h3>
          <p class="text-gray-600 mb-6">不用买很贵的，简单的工具就能做出营养美味的辅食。</p>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 text-center">
            <div class="p-4 bg-gray-50 rounded-lg">
              <div class="text-4xl mb-2">🌪️</div>
              <h4 class="font-bold">辅食机/搅拌棒</h4>
              <p class="text-sm text-gray-500">打泥神器</p>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
              <div class="text-4xl mb-2">💨</div>
              <h4 class="font-bold">蒸锅</h4>
              <p class="text-sm text-gray-500">保留营养</p>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
              <div class="text-4xl mb-2">🥣</div>
              <h4 class="font-bold">研磨碗/叉子</h4>
              <p class="text-sm text-gray-500">处理软食材</p>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg">
              <div class="text-4xl mb-2">🔪</div>
              <h4 class="font-bold">小菜板和刀</h4>
              <p class="text-sm text-gray-500">生熟分开</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">2. 从“喝”到“吃”的演变</h3>
          <p class="text-gray-600 mb-6">辅食质地的变化，是锻炼宝宝咀嚼和吞咽能力的关键一步。下面我们用生活中的例子来理解。</p>
          
          <div class="space-y-8">
            <!-- 6-7个月 -->
            <div class="p-6 rounded-lg highlight-box">
              <h4 class="text-xl font-bold mb-3">第一阶段 (6-7个月): 顺滑泥糊状</h4>
              <p class="mb-4"><strong>目标：</strong>让宝宝习惯用勺子吃东西，学会吞咽。</p>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold mb-2">质地像什么？</h5>
                  <p>像浓稠的酸奶，用勺子舀起来，倾斜后会缓慢地流下来。</p>
                  <h5 class="font-bold mt-4 mb-2">怎么做？</h5>
                  <p>食材蒸熟后，用搅拌机打成非常细腻的泥，可以加点温水或母乳调节稀稠。</p>
                </div>
                <div>
                  <h5 class="font-bold mb-2">举个例子：</h5>
                  <ul class="list-disc list-inside text-gray-700 space-y-1">
                    <li><strong>苹果泥:</strong> 苹果去皮切块蒸熟，加一点点水打成泥。</li>
                    <li><strong>高铁米粉:</strong> 按说明用温水冲调，调成顺滑的糊状。</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 8-9个月 -->
            <div class="p-6 rounded-lg warning-box">
              <h4 class="text-xl font-bold mb-3">第二阶段 (8-9个月): 细小颗粒状</h4>
               <p class="mb-4"><strong>目标：</strong>鼓励宝宝用舌头和口腔配合，感受食物的颗粒感。</p>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold mb-2">质地像什么？</h5>
                  <p>像很烂的粥，或者超市里卖的果粒酸奶，有小小的、软软的颗粒。</p>
                  <h5 class="font-bold mt-4 mb-2">怎么做？</h5>
                  <p>蒸熟的食物用叉子碾压，或者用搅拌机点动打几下，不要打太久。</p>
                </div>
                <div>
                  <h5 class="font-bold mb-2">举个例子：</h5>
                  <ul class="list-disc list-inside text-gray-700 space-y-1">
                    <li><strong>鱼肉泥:</strong> 蒸熟的三文鱼去刺，用叉子压碎。</li>
                    <li><strong>南瓜粥:</strong> 南瓜蒸熟压成泥，混进煮得烂烂的白米粥里。</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 10-12个月 -->
            <div class="p-6 rounded-lg" style="border-left: 4px solid #A57C4F; background: linear-gradient(135deg, #f7f4f2 0%, #f3efeb 100%);">
              <h4 class="text-xl font-bold mb-3">第三阶段 (10-12个月): 软烂小块状</h4>
              <p class="mb-4"><strong>目标：</strong>开始练习用小牙床咀嚼，为自己吃饭做准备。</p>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-bold mb-2">质地像什么？</h5>
                  <p>煮得很软的胡萝卜块，用手指轻轻一捏就能烂的大小和硬度。</p>
                  <h5 class="font-bold mt-4 mb-2">怎么做？</h5>
                  <p>食材蒸或煮到非常软烂，然后用刀切成宝宝能用手抓起的小丁或短条。</p>
                </div>
                <div>
                  <h5 class="font-bold mb-2">举个例子：</h5>
                  <ul class="list-disc list-inside text-gray-700 space-y-1">
                    <li><strong>手指胡萝卜:</strong> 胡萝卜切成小指粗细的条，蒸到软烂。</li>
                    <li><strong>小块豆腐:</strong> 韧豆腐切成小丁，在水里煮2分钟。</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Tab 2: 辅食种类 -->
      <section id="tab2" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">辅食种类</h2>
        <div class="highlight-box p-6 rounded-lg">
            <h4 class="text-xl font-bold mb-2">💡 添加原则：从一到多</h4>
            <p class="text-lg">每次只给宝宝吃一种新的食物，连续吃2-3天，看看有没有不舒服（比如拉肚子、身上起红点），没问题再加下一种。</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">1. 第一口吃什么？</h3>
          <div class="warning-box p-6 rounded-lg">
            <h4 class="text-xl font-bold mb-2">高铁食物！</h4>
            <p class="text-lg">宝宝6个月后，从妈妈肚子里带出来的铁快用完了，要赶紧补上。首选是<strong>强化铁的婴儿米粉</strong>，或者把<strong>红肉、猪肝</strong>做成泥给宝宝吃。</p>
          </div>
        </div>
        <div>
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">2. 各月龄食物推荐</h3>
          <div id="age-food-accordion" class="grid grid-cols-1 md:grid-cols-3 gap-6">
          </div>
        </div>
      </section>

      <!-- Tab 3: 食谱推荐 -->
      <section id="tab3" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">食谱推荐</h2>
        <p class="text-lg text-gray-700">这里有一些简单又营养的食谱，妈妈们可以照着做，也可以发挥创意！</p>
        <div id="recipe-accordion" class="grid grid-cols-1 md:grid-cols-3 gap-6">
        </div>
      </section>

      <!-- Tab 4: 其它问题 -->
      <section id="tab4" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">其它问题</h2>
        <div id="qa-accordion" class="space-y-3">
            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
                <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                    <span>宝宝不爱吃饭怎么办？</span>
                    <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
                </button>
                <div class="accordion-content">
                    <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                        <strong>首先别着急，也别强迫:</strong><br>
                        1. <strong>尊重宝宝：</strong>他可能只是暂时不想吃，强迫会让他更讨厌吃饭。<br>
                        2. <strong>做好榜样：</strong>全家人都香喷喷地吃饭，宝宝看着也会有食欲。<br>
                        3. <strong>增加趣味：</strong>把食物做成可爱的形状，或者让他自己用手抓着吃。<br>
                        4. <strong>耐心最重要：</strong>一种新食物，宝宝可能要试10多次才接受，很正常！
                    </div>
                </div>
            </div>
            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
                <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                    <span>哪些食物容易让宝宝过敏？</span>
                    <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
                </button>
                <div class="accordion-content">
                    <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                        <p class="mb-4">常见的“高敏食物”有：<strong>鸡蛋、花生、牛奶、海鲜、芒果</strong>等。</p>
                        <strong>新观念：</strong>现在不建议特意推迟添加这些食物。满6个月后，在宝宝健康的时候，就可以少量尝试了，这反而可能降低过敏风险。<br>
                        <strong>记住：</strong>每次只加一种新的，观察2-3天，确认没问题再加下一种。如果家族有过敏史，可以先咨询医生。
                    </div>
                </div>
            </div>
            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
                <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                    <span>怎么防止宝宝被噎到？</span>
                    <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
                </button>
                <div class="accordion-content">
                    <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                        <strong>这些食物要小心处理：</strong><br>
                        • <strong>小而圆的：</strong>像葡萄、圣女果，一定要竖着切成四瓣。<br>
                        • <strong>硬的：</strong>像坚果、硬糖，绝对不能给小宝宝吃。胡萝卜、苹果要蒸软或擦成丝。<br>
                        • <strong>黏的：</strong>像果冻、汤圆，很容易粘在喉咙里，非常危险。<br>
                        <strong>最重要：</strong>宝宝吃饭时必须坐直，大人要一直看着，不能让他边玩边吃。
                    </div>
                </div>
            </div>
             <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
                <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                    <span>做好的辅食怎么保存？</span>
                    <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
                </button>
                <div class="accordion-content">
                    <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                        <strong>记住几个数字就行：</strong><br>
                        • <strong>冷藏（冰箱保鲜层）：</strong>最多放2天。<br>
                        • <strong>冷冻（冰箱冷冻层）：</strong>最多放1-2个月。<br>
                        <br>
                        <strong>小技巧：</strong>用辅食盒分装成一小份一小份再冻起来，吃多少取多少，很方便。吃过的、沾了口水的辅食就不要再留了哦！
                    </div>
                </div>
            </div>
        </div>
      </section>
    </main>
  </div>

  <script>
    const sections = [
      { id: 'tab1', title: '辅食制作', icon: '🥣' },
      { id: 'tab2', title: '辅食种类', icon: '🥕' },
      { id: 'tab3', title: '食谱推荐', icon: '🍲' },
      { id: 'tab4', title: '其它问题', icon: '❓' }
    ];

    const ageGuideData = {
        "6-8": {
          foods: "<strong>主食：</strong>强化铁米粉<br><strong>蔬菜：</strong>土豆、南瓜、胡萝卜、西兰花<br><strong>水果：</strong>苹果、香蕉、牛油果<br><strong>肉类：</strong>猪肝泥、红肉泥",
          recipes: [
            { name: "经典高铁米粉糊", desc: "取适量强化铁米粉，用温水、母乳或配方奶冲调成顺滑糊状。" },
            { name: "补铁猪肝泥", desc: "新鲜猪肝蒸熟后，加少量温水用辅食机打成细腻泥状。" },
            { name: "牛油果香蕉泥", desc: "成熟的牛油果和香蕉用叉子一同碾压成泥，无需烹饪。" },
            { name: "蛋黄初体验", desc: "鸡蛋煮熟，取1/4蛋黄压碎，用少量温水调成糊状。" }
          ]
        },
        "9-11": {
          foods: "<strong>主食：</strong>软饭、烂面条、小馄饨<br><strong>蔬菜：</strong>菠菜、白菜、西红柿<br><strong>水果：</strong>桃子、草莓、蓝莓<br><strong>肉类：</strong>鸡肉、鱼肉（去刺）、虾肉",
          recipes: [
            { name: "三色猪肝粥", desc: "大米煮成软烂的粥，加入猪肝泥、胡萝卜末和菠菜碎煮熟。" },
            { name: "BLW明星：软烂蔬菜条", desc: "胡萝卜、红薯等切成条状蒸至软烂，让宝宝自己抓着吃。" },
            { name: "鸡肉末菠菜烂面", desc: "婴儿面条煮至软烂，加入煮熟的鸡肉末和菠菜碎。" },
            { name: "手指食物：豆腐蔬菜饼", desc: "豆腐压泥，混合鸡蛋和蔬菜末，两面煎熟成小饼。" }
          ]
        },
        "12+": {
          foods: "<strong>主食：</strong>软饭、馒头、饺子<br><strong>蔬菜：</strong>各种绿叶蔬菜、菌菇<br><strong>水果：</strong>多数水果都可以尝试<br><strong>其它：</strong>酸奶、奶酪、豆制品",
          recipes: [
            { name: "迷你蔬菜肉丸", desc: "猪肉末混合蔬菜末搓成小丸子，可煮汤、蒸或烩。" },
            { name: "蔬菜三文鱼焖饭", desc: "三文鱼丁、蔬菜末与大米一同放入电饭煲焖熟。" },
            { name: "趣味早餐：鸡蛋蔬菜烘蛋", desc: "蛋奶液混合蔬菜碎，倒入玛芬烤盘烤熟，方便手抓。" },
            { name: "牛肉胡萝卜软饭", desc: "牛肉末和胡萝卜丁炒软后，加入软米饭焖煮入味。" }
          ]
        }
    };

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });
        window.scrollTo(0, 0);
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      function setupAccordion(containerId) {
        const accordionContainer = document.getElementById(containerId);
        if (!accordionContainer) return;

        accordionContainer.addEventListener('click', function (e) {
          const button = e.target.closest('.accordion-button');
          if (button) {
            const content = button.nextElementSibling;
            const isOpen = button.classList.contains('open');
            
            // Optional: close others when one opens
            // accordionContainer.querySelectorAll('.accordion-button').forEach(btn => {
            //   if (btn !== button) {
            //      btn.classList.remove('open');
            //      btn.nextElementSibling.style.maxHeight = null;
            //   }
            // });

            if (isOpen) {
              button.classList.remove('open');
              content.style.maxHeight = null;
            } else {
              button.classList.add('open');
              content.style.maxHeight = content.scrollHeight + "px";
            }
          }
        });
      }
      
      function createAccordionItem(title, content, parentElement) {
          const item = document.createElement('div');
          item.className = "border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition";
          item.innerHTML = `
              <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                  <span>${title}</span>
                  <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                  <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                      ${content}
                  </div>
              </div>
          `;
          parentElement.appendChild(item);
      }

      function createInfoCard(title, content, parentElement) {
          const item = document.createElement('div');
          item.className = "bg-white rounded-lg shadow-md p-6 flex flex-col";
          item.innerHTML = `
              <h4 class="text-xl font-bold text-[#A57C4F] mb-4">${title}</h4>
              <div class="text-gray-700 text-base leading-relaxed">${content}</div>
          `;
          parentElement.appendChild(item);
      }

      // Populate Food Types Tab
      const ageFoodContainer = document.getElementById('age-food-accordion');
      createInfoCard("6-8个月：吞咽与探索期", ageGuideData["6-8"].foods, ageFoodContainer);
      createInfoCard("9-11个月：咀嚼与自主期", ageGuideData["9-11"].foods, ageFoodContainer);
      createInfoCard("12个月以上：融入家庭餐桌", ageGuideData["12+"].foods, ageFoodContainer);
      
      // Populate Recipes Tab
      const recipeContainer = document.getElementById('recipe-accordion');
      Object.keys(ageGuideData).forEach(age => {
          const recipesContent = ageGuideData[age].recipes.map(r => `
            <div class="mb-4">
                <h4 class="font-bold">${r.name}</h4>
                <p>${r.desc}</p>
            </div>
          `).join('');
          createInfoCard(`${age}个月食谱`, recipesContent, recipeContainer);
      });

      // Setup all accordions
      setupAccordion('qa-accordion');
      
      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1); // 去掉#符号
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        // Activate first tab
        activateSection('tab1');
      }

      // 监听hash变化（支持浏览器前进后退）
      window.addEventListener('hashchange', handleHashChange);

      // Chart.js
      const ctx = document.getElementById('textureChart').getContext('2d');
      const textureData = {
        labels: ['6-8个月', '9-11个月', '12个月+'],
        datasets: [
          { label: '泥糊状', data: [100, 20, 0], backgroundColor: '#EACDA4' },
          { label: '碎末/稠泥', data: [0, 60, 20], backgroundColor: '#A57C4F' },
          { label: '小块/手指食物', data: [0, 20, 80], backgroundColor: '#8FBC8F' }
        ]
      };
      const textureChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'top' }, tooltip: { callbacks: { label: ctx => `${ctx.dataset.label}: ${ctx.parsed.y}%` }}},
        scales: { x: { stacked: true }, y: { stacked: true, ticks: { callback: value => value + "%" } } }
      };
      new Chart(ctx, { type: 'bar', data: textureData, options: textureChartOptions });
    });
  </script>
</body>
</html>