<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>新手妈妈的婴儿便便与尿布指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }

    .accordion-button.open .accordion-arrow {
      transform: rotate(180deg);
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }

    .color-swatch {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .color-swatch:hover {
      transform: scale(1.05);
    }

    .color-swatch.active {
      transform: scale(1.1);
      box-shadow: 0 0 0 4px #A57C4F;
      z-index: 10;
    }

    .warning-swatch::after {
      content: '⚠️';
      position: absolute;
      top: -8px;
      right: -8px;
      font-size: 1.2rem;
      line-height: 1;
    }

    .chart-container {
      position: relative;
      width: 100%;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      height: 320px;
      max-height: 400px;
    }

    @media (min-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">宝宝便便指南</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 尿布更换指南 -->
      <section id="diaper_change" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">尿布更换指南</h2>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">正确更换尿布的7个步骤</h3>
          
          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">💡 记住这句话</h4>
            <p class="text-lg text-center font-bold">安全第一，从前向后，拍干涂厚，松紧适度</p>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">1</div>
              <h5 class="font-bold text-sm mb-1">准备工作</h5>
              <p class="text-xs text-gray-600">新尿布、湿巾、护臀膏放在手边</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">2</div>
              <h5 class="font-bold text-sm mb-1">垫新取旧</h5>
              <p class="text-xs text-gray-600">新尿布垫在下面，再解开旧的</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">3</div>
              <h5 class="font-bold text-sm mb-1">从前向后</h5>
              <p class="text-xs text-gray-600">特别是女宝宝，防止感染</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">4</div>
              <h5 class="font-bold text-sm mb-1">拍干风干</h5>
              <p class="text-xs text-gray-600">轻拍或晾一会儿，别擦</p>
            </div>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">5</div>
              <h5 class="font-bold text-sm mb-1">厚涂护臀膏</h5>
              <p class="text-xs text-gray-600">像抹奶油一样厚厚一层</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">6</div>
              <h5 class="font-bold text-sm mb-1">穿好检查</h5>
              <p class="text-xs text-gray-600">腰部能伸入1-2指，防漏边拉出</p>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">7</div>
              <h5 class="font-bold text-sm mb-1">清理洗手</h5>
              <p class="text-xs text-gray-600">处理脏尿布，彻底洗手</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">尿布疹怎么办？</h3>
          
          <div id="rash-accordion" class="space-y-3">
            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button class="accordion-button w-full text-left p-4 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>🔴 红屁屁（最常见）</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-4 border-t-2 border-gray-100 text-gray-700 leading-relaxed">
                  <p><strong>什么样子：</strong>屁屁、大腿根部发红，摸起来平平的，皮肤褶皱里没有</p>
                  <p class="mt-2"><strong>怎么办：</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li>更勤快地换尿布</li>
                    <li>每次都要拍干</li>
                    <li>护臀膏涂得更厚</li>
                    <li>让宝宝光着屁屁晾一会儿</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button class="accordion-button w-full text-left p-4 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>🦠 真菌感染（需要看医生）</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-4 border-t-2 border-gray-100 text-gray-700 leading-relaxed">
                  <p><strong>什么样子：</strong>鲜红色，皮肤褶皱里也有，周围有小红点或白点</p>
                  <p class="mt-2"><strong>怎么办：</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li>2-3天没好转就看医生</li>
                    <li>可能需要用药膏</li>
                    <li>保持干燥很重要</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
              <button class="accordion-button w-full text-left p-4 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
                <span>🤧 过敏反应（比较少见）</span>
                <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
              </button>
              <div class="accordion-content">
                <div class="p-4 border-t-2 border-gray-100 text-gray-700 leading-relaxed">
                  <p><strong>什么样子：</strong>痒痒的，有鳞屑，正好在尿布或湿巾碰到的地方</p>
                  <p class="mt-2"><strong>怎么办：</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li>换个牌子的尿布试试</li>
                    <li>换个牌子的湿巾试试</li>
                    <li>看看护臀膏是不是过敏</li>
                    <li>问问医生</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 便便颜色解读 -->
      <section id="poop_colors" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">颜色解读</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">🎯 一句话记住</h3>
          <p class="text-lg text-center font-bold">黄色、棕色、绿色都正常，黑色、白色、红色要看医生</p>
        </div>

        <!-- 便便颜色卡片 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">便便颜色解读</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            <div class="border-2 border-red-200 bg-red-50 rounded-lg p-4 flex flex-col">
              <div class="flex items-start mb-3">
                <div class="relative w-12 h-12 rounded-lg mr-4 flex-shrink-0 warning-swatch" style="background-color: #333333;"></div>
                <h4 class="font-bold text-lg text-red-800">黑色：胎便或有问题</h4>
              </div>
              <p class="text-gray-700 text-base leading-relaxed">刚出生1-3天的黑色粘稠便便是正常的胎便。如果胎便拉完后又出现黑色，可能是出血，要马上看医生。</p>
            </div>

            <div class="border-2 border-red-200 bg-red-50 rounded-lg p-4 flex flex-col">
              <div class="flex items-start mb-3">
                <div class="relative w-12 h-12 rounded-lg mr-4 flex-shrink-0 warning-swatch" style="background-color: #F3F4F6; border: 2px solid #ddd;"></div>
                <h4 class="font-bold text-lg text-red-800">灰白色：紧急情况！</h4>
              </div>
              <p class="text-gray-700 text-base leading-relaxed">像白土一样的便便很危险，可能是胆道有问题。这是紧急情况，必须马上看医生，不能等！</p>
            </div>

            <div class="border-2 border-red-200 bg-red-50 rounded-lg p-4 flex flex-col">
              <div class="flex items-start mb-3">
                <div class="relative w-12 h-12 rounded-lg mr-4 flex-shrink-0 warning-swatch" style="background-color: #BE123C;"></div>
                <h4 class="font-bold text-lg text-red-800">红色：马上看医生！</h4>
              </div>
              <p class="text-gray-700 text-base leading-relaxed">便便里有血丝可能是肛裂或过敏。如果像果酱一样，可能是肠套叠，非常危险，必须马上去医院！</p>
            </div>

            <div class="border rounded-lg p-4 flex flex-col">
              <div class="flex items-start mb-3">
                <div class="w-12 h-12 rounded-lg mr-4 flex-shrink-0" style="background-color: #EAB308;"></div>
                <h4 class="font-bold text-lg">芥末黄：正常（母乳宝宝）</h4>
              </div>
              <p class="text-gray-700 text-base leading-relaxed">稀稀的，有小颗粒，这是健康的母乳宝宝便便。完全正常，不用担心。</p>
            </div>

            <div class="border rounded-lg p-4 flex flex-col">
              <div class="flex items-start mb-3">
                <div class="w-12 h-12 rounded-lg mr-4 flex-shrink-0" style="background-color: #854D0E;"></div>
                <h4 class="font-bold text-lg">棕色：正常（配方奶宝宝）</h4>
              </div>
              <p class="text-gray-700 text-base leading-relaxed">像花生酱一样稠，这是健康的配方奶宝宝便便。完全正常。</p>
            </div>

            <div class="border rounded-lg p-4 flex flex-col">
              <div class="flex items-start mb-3">
                <div class="w-12 h-12 rounded-lg mr-4 flex-shrink-0" style="background-color: #166534;"></div>
                <h4 class="font-bold text-lg">绿色：通常正常</h4>
              </div>
              <p class="text-gray-700 text-base leading-relaxed">配方奶铁质高、妈妈吃了绿色蔬菜、或者消化快都会绿色。宝宝没有其他不舒服就不用担心。</p>
            </div>

          </div>
        </div>

        <!-- 尿液颜色卡片 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">尿液颜色解读</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            <div class="border rounded-lg p-4 flex items-center">
              <div class="w-16 h-16 rounded-lg mr-4 flex-shrink-0 flex items-center justify-center text-4xl" style="background-color: #FFFBEB; border: 2px solid #ddd;">💧</div>
              <div>
                <h4 class="font-bold text-lg">无色/淡黄色：很好！</h4>
                <p class="text-gray-700 text-base leading-relaxed">说明宝宝水分够，很健康。继续保持现在的喂奶节奏就行。</p>
              </div>
            </div>

            <div class="border border-yellow-400 bg-yellow-50 rounded-lg p-4 flex items-center">
              <div class="w-16 h-16 rounded-lg mr-4 flex-shrink-0 flex items-center justify-center text-4xl" style="background-color: #FFC700;">💧</div>
              <div>
                <h4 class="font-bold text-lg text-yellow-800">深黄色：有点缺水</h4>
                <p class="text-gray-700 text-base leading-relaxed">尿液比较浓，说明需要多喂点奶。天热或发烧时经常这样，多喂几次就好了。</p>
              </div>
            </div>

            <div class="border rounded-lg p-4 flex items-center">
              <div class="w-16 h-16 rounded-lg mr-4 flex-shrink-0 flex items-center justify-center text-4xl" style="background-color: #FECACA;">💧</div>
              <div>
                <h4 class="font-bold text-lg">粉红/橘红结晶：新生儿常见</h4>
                <p class="text-gray-700 text-base leading-relaxed">这是尿酸盐结晶，新生儿很常见，不用担心。多喂奶，过几天就没有了。如果一直有，问问医生。</p>
              </div>
            </div>

            <div class="border-2 border-red-200 bg-red-50 rounded-lg p-4 flex items-center">
              <div class="w-16 h-16 rounded-lg mr-4 flex-shrink-0 flex items-center justify-center text-4xl text-white" style="background-color: #DC2626;">💧</div>
              <div>
                <h4 class="font-bold text-lg text-red-800">红色：马上看医生！</h4>
                <p class="text-gray-700 text-base leading-relaxed">这可能是血尿，需要立即看医生。可能是感染或其他问题，不能等。</p>
              </div>
            </div>

          </div>
        </div>
      </section>

      <!-- 便便频率和问题 -->
      <section id="frequency_problems" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">便便频率和常见问题</h2>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">宝宝多久拉一次算正常？</h3>
          
          <div class="flex justify-center gap-4 mb-6">
            <button id="breastfed-btn" class="px-6 py-3 bg-[#A57C4F] text-white rounded-full font-medium">
              🤱 母乳喂养
            </button>
            <button id="formula-btn" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-full font-medium">
              🍼 配方奶喂养
            </button>
          </div>
          
          <div class="chart-container">
            <canvas id="frequencyChart"></canvas>
          </div>
          
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 class="font-bold text-blue-800 mb-2">💡 重要提醒：</h4>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• 母乳宝宝可能好几天才拉一次，这叫"攒肚"，是正常的</li>
              <li>• 配方奶宝宝通常比较规律，每天1-2次</li>
              <li>• 重要的是便便的形状和颜色，不是次数</li>
            </ul>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold text-[#D99477] mb-4">🧱 便秘了怎么办？</h3>
            <div class="warning-box p-4 rounded-lg mb-4">
              <p class="font-bold">记住：看便便硬不硬，不是看多久没拉！</p>
            </div>
            <div class="space-y-3">
              <div>
                <h4 class="font-bold text-gray-800">什么是便秘？</h4>
                <p class="text-sm text-gray-600">便便干硬像小石子，宝宝拉得很费劲很痛苦</p>
              </div>
              <div>
                <h4 class="font-bold text-gray-800">怎么办？</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 给宝宝做腹部按摩</li>
                  <li>• 帮宝宝做蹬自行车运动</li>
                  <li>• 如果加了辅食，可以试试西梅泥</li>
                  <li>• 便便带血或宝宝不吃奶要看医生</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold text-[#8FBC8F] mb-4">💧 拉肚子了怎么办？</h3>
            <div class="highlight-box p-4 rounded-lg mb-4">
              <p class="font-bold">记住：最重要的是防止脱水！</p>
            </div>
            <div class="space-y-3">
              <div>
                <h4 class="font-bold text-gray-800">什么是拉肚子？</h4>
                <p class="text-sm text-gray-600">突然变得很稀很水，次数比平时多很多</p>
              </div>
              <div>
                <h4 class="font-bold text-gray-800">怎么办？</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• <strong>继续喂奶</strong>，不要停</li>
                  <li>• 更频繁地喂，补充水分</li>
                  <li>• 观察宝宝有没有脱水（尿少、哭没眼泪）</li>
                  <li>• 发烧或便便有血要马上看医生</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <script>
    const sections = [
      { id: 'diaper_change', title: '尿布更换', icon: '🍼' },
      { id: 'poop_colors', title: '颜色解读', icon: '🎨' },
      { id: 'frequency_problems', title: '频率问题', icon: '📊' }
    ];

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

        window.scrollTo(0, 0);

        if (targetId === 'diaper_change') {
          setupAccordion();
        } else if (targetId === 'frequency_problems') {
          setupChart();
        }
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1); // 去掉#符号
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        // 默认激活第一个标签
        activateSection('diaper_change');
      }

      // 监听hash变化（支持浏览器前进后退）
      window.addEventListener('hashchange', handleHashChange);

      // 手风琴功能
      function setupAccordion() {
        const accordionContainer = document.getElementById('rash-accordion');
        if (!accordionContainer) return;

        // Set up individual toggling
        accordionContainer.addEventListener('click', function (e) {
          const button = e.target.closest('.accordion-button');
          if (button) {
            button.classList.toggle('open');
            const content = button.nextElementSibling;
            if (button.classList.contains('open')) {
              content.style.maxHeight = content.scrollHeight + "px";
            } else {
              content.style.maxHeight = null;
            }
          }
        });

        // Open all by default on page load
        const buttons = accordionContainer.querySelectorAll('.accordion-button');
        buttons.forEach(button => {
          button.classList.add('open');
          const content = button.nextElementSibling;
          content.style.maxHeight = content.scrollHeight + "px";
        });

        // Adjust height on resize for open accordions
        window.addEventListener('resize', () => {
            accordionContainer.querySelectorAll('.accordion-button.open').forEach(button => {
                const content = button.nextElementSibling;
                content.style.maxHeight = content.scrollHeight + "px";
            });
        });
      }

      // 图表功能
      function setupChart() {
        const freqCtx = document.getElementById('frequencyChart').getContext('2d');
        const chartData = {
          breastfed: {
            labels: ['新生儿 (0-6周)', '婴儿期 (6周-6月)', '大婴儿 (6-12月)'],
            poop: [5, 0.5, 1.5],
            pee: [7, 7, 7]
          },
          formula: {
            labels: ['新生儿 (0-6周)', '婴儿期 (6周-6月)', '大婴儿 (6-12月)'],
            poop: [2.5, 1, 1.5],
            pee: [7, 7, 7]
          }
        };

        let frequencyChart = new Chart(freqCtx, {
          type: 'bar',
          data: {
            labels: chartData.breastfed.labels,
            datasets: [
              {
                label: '每天便便次数',
                data: chartData.breastfed.poop,
                backgroundColor: '#A57C4F',
                borderColor: '#A57C4F',
                borderWidth: 1
              },
              {
                label: '每天尿尿次数',
                data: chartData.breastfed.pee,
                backgroundColor: '#8FBC8F',
                borderColor: '#8FBC8F',
                borderWidth: 1
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                title: { display: true, text: '每天次数' }
              }
            },
            plugins: {
              tooltip: {
                callbacks: {
                  title: function (context) {
                    let title = context[0].label;
                    if (title.includes('婴儿期') && currentFeedType === 'breastfed') {
                      return title + ' (可能攒肚)';
                    }
                    return title;
                  },
                  label: function (context) {
                    let label = context.dataset.label || '';
                    if (label) {
                      label += ': ';
                    }
                    if (context.parsed.y !== null) {
                      if (context.dataset.label.includes('便便') && context.label.includes('婴儿期') && currentFeedType === 'breastfed') {
                        label += '可能7-10天才1次';
                      } else {
                        label += context.parsed.y + '次';
                      }
                    }
                    return label;
                  }
                }
              }
            }
          }
        });

        let currentFeedType = 'breastfed';
        const breastfedBtn = document.getElementById('breastfed-btn');
        const formulaBtn = document.getElementById('formula-btn');

        function updateChart(feedType) {
          currentFeedType = feedType;
          const data = chartData[feedType];
          frequencyChart.data.datasets[0].data = data.poop;
          frequencyChart.data.datasets[1].data = data.pee;
          frequencyChart.update();

          if (feedType === 'breastfed') {
            breastfedBtn.classList.add('bg-[#A57C4F]', 'text-white');
            breastfedBtn.classList.remove('bg-gray-200', 'text-gray-700');
            formulaBtn.classList.add('bg-gray-200', 'text-gray-700');
            formulaBtn.classList.remove('bg-[#A57C4F]', 'text-white');
          } else {
            formulaBtn.classList.add('bg-[#A57C4F]', 'text-white');
            formulaBtn.classList.remove('bg-gray-200', 'text-gray-700');
            breastfedBtn.classList.add('bg-gray-200', 'text-gray-700');
            breastfedBtn.classList.remove('bg-[#A57C4F]', 'text-white');
          }
        }

        breastfedBtn.addEventListener('click', () => updateChart('breastfed'));
        formulaBtn.addEventListener('click', () => updateChart('formula'));
      }
    });
  </script>
</body>

</html>