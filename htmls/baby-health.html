<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>宝宝健康安全指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }

    .chart-container {
      position: relative;
      width: 100%;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      height: 300px;
    }

    @media (min-width: 640px) {
      .chart-container {
        height: 350px;
      }
    }

    .card-hover {
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">宝宝健康安全指南</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 常见疾病 Section -->
      <section id="common_illnesses" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">常见疾病</h2>
        
        <!-- Categorized Illnesses -->
        <div id="illness-categories">
          <!-- Categories and cards will be injected here by JS -->
        </div>
      </section>

      <!-- 发烧与急救 Section -->
      <section id="first_aid" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">发烧与急救知识</h2>
        
        <div id="aid-grid" class="space-y-6">
          <!-- First aid cards will be injected here by JS -->
        </div>
      </section>

      <!-- 疫苗接种与不良反应 Section -->
      <section id="vaccination" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">疫苗接种与不良反应</h2>
        
        <div class="space-y-6">

          <!-- 疫苗接种时间表 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">💉 详细疫苗接种时间表</h3>

            <div class="chart-container mb-6">
              <canvas id="vaccineChart"></canvas>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
                <h5 class="font-bold text-blue-800 mb-2">🍼 出生时</h5>
                <ul class="text-sm text-blue-700">
                  <li>• 乙肝疫苗第一针</li>
                  <li>• 卡介苗（结核病）</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-green-200 rounded-lg bg-green-50">
                <h5 class="font-bold text-green-800 mb-2">👶 2个月</h5>
                <ul class="text-sm text-green-700">
                  <li>• 五联疫苗第一针</li>
                  <li>• 肺炎疫苗第一针</li>
                  <li>• 轮状病毒疫苗</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-yellow-200 rounded-lg bg-yellow-50">
                <h5 class="font-bold text-yellow-800 mb-2">👶 4个月</h5>
                <ul class="text-sm text-yellow-700">
                  <li>• 五联疫苗第二针</li>
                  <li>• 肺炎疫苗第二针</li>
                  <li>• 轮状病毒疫苗</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-purple-200 rounded-lg bg-purple-50">
                <h5 class="font-bold text-purple-800 mb-2">👶 6个月</h5>
                <ul class="text-sm text-purple-700">
                  <li>• 五联疫苗第三针</li>
                  <li>• 乙肝疫苗第三针</li>
                  <li>• 流感疫苗（每年）</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-red-200 rounded-lg bg-red-50">
                <h5 class="font-bold text-red-800 mb-2">🎂 12个月</h5>
                <ul class="text-sm text-red-700">
                  <li>• 麻腮风疫苗</li>
                  <li>• 水痘疫苗</li>
                  <li>• 甲肝疫苗</li>
                </ul>
              </div>

              <div class="p-4 border-2 border-orange-200 rounded-lg bg-orange-50">
                <h5 class="font-bold text-orange-800 mb-2">👶 15-18个月</h5>
                <ul class="text-sm text-orange-700">
                  <li>• 百白破加强针</li>
                  <li>• Hib加强针</li>
                  <li>• 肺炎疫苗加强针</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 常见不良反应 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">😷 常见不良反应与处理</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-green-50 p-6 rounded-lg border-l-4 border-green-500">
                <h4 class="text-xl font-bold text-green-800 mb-4">✅ 轻微反应（正常）</h4>
                <ul class="space-y-2 text-gray-700">
                  <li>• <strong>接种部位：</strong>红肿、疼痛、硬结</li>
                  <li>• <strong>全身症状：</strong>低热（<38.5°C）、轻微烦躁</li>
                  <li>• <strong>持续时间：</strong>1-3天自然消退</li>
                  <li>• <strong>处理方法：</strong>局部冷敷，适当休息</li>
                </ul>
              </div>

              <div class="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500">
                <h4 class="text-xl font-bold text-yellow-800 mb-4">⚠️ 中度反应（需观察）</h4>
                <ul class="space-y-2 text-gray-700">
                  <li>• <strong>发热：</strong>38.5-39°C，持续1-2天</li>
                  <li>• <strong>接种部位：</strong>肿胀直径>2.5cm</li>
                  <li>• <strong>行为改变：</strong>哭闹不止，拒食</li>
                  <li>• <strong>处理方法：</strong>物理降温，密切观察</li>
                </ul>
              </div>
            </div>

            <div class="bg-red-50 p-6 rounded-lg border-l-4 border-red-500 mt-6">
              <h4 class="text-xl font-bold text-red-800 mb-4">🚨 严重反应（立即就医）</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ul class="space-y-2 text-gray-700">
                  <li>• <strong>过敏反应：</strong>皮疹、呼吸困难、面部肿胀</li>
                  <li>• <strong>高热：</strong>体温>39°C，持续超过48小时</li>
                  <li>• <strong>神经症状：</strong>抽搐、意识改变</li>
                </ul>
                <ul class="space-y-2 text-gray-700">
                  <li>• <strong>严重局部反应：</strong>接种部位严重肿胀、化脓</li>
                  <li>• <strong>全身症状：</strong>精神萎靡、拒食超过24小时</li>
                  <li>• <strong>罕见反应：</strong>肌张力低下、持续哭闹>3小时</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 接种注意事项 -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">📋 接种前后注意事项</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
                <h4 class="text-xl font-bold text-blue-800 mb-4">📅 接种前准备</h4>
                <ul class="space-y-2 text-gray-700">
                  <li>• 确保宝宝身体健康，无发热</li>
                  <li>• 携带疫苗接种证</li>
                  <li>• 告知医生过敏史</li>
                  <li>• 避免空腹接种</li>
                  <li>• 穿宽松易脱的衣服</li>
                </ul>
              </div>

              <div class="bg-green-50 p-6 rounded-lg border-2 border-green-200">
                <h4 class="text-xl font-bold text-green-800 mb-4">⏰ 接种时配合</h4>
                <ul class="space-y-2 text-gray-700">
                  <li>• 如实告知宝宝近期健康状况</li>
                  <li>• 协助医生固定宝宝</li>
                  <li>• 接种后在医院观察30分钟</li>
                  <li>• 注意观察宝宝反应</li>
                  <li>• 有异常立即告知医生</li>
                </ul>
              </div>

              <div class="bg-purple-50 p-6 rounded-lg border-2 border-purple-200">
                <h4 class="text-xl font-bold text-purple-800 mb-4">🏠 接种后护理</h4>
                <ul class="space-y-2 text-gray-700">
                  <li>• 保持接种部位清洁干燥</li>
                  <li>• 24小时内避免洗澡</li>
                  <li>• 多给宝宝喝水</li>
                  <li>• 避免剧烈活动</li>
                  <li>• 密切观察72小时</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="warning-box p-6 rounded-lg">
            <h4 class="font-bold text-red-800 mb-3">⚠️ 重要提醒</h4>
            <div class="text-red-700 space-y-2">
              <p>• 具体疫苗种类和时间可能因地区而异，请以当地疾控中心和儿科医生的建议为准</p>
              <p>• 如有发热或生病，请推迟接种，待宝宝完全康复后再接种</p>
              <p>• 疫苗接种是预防疾病的有效手段，获益远远大于风险</p>
              <p>• 如对疫苗有疑虑，请咨询专业医生，不要轻信网络谣言</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 健康检查 Section -->
      <section id="health_checks" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">健康检查</h2>

        <!-- 健康检查的重要性 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">🗓️ 宝宝体检时间表</h3>

          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">💡 为什么要定期体检</h4>
            <p class="text-lg mb-4">定期带宝宝体检，能<strong>及早发现问题</strong>、<strong>预防疾病</strong>、<strong>监测发育</strong>，让宝宝健康成长。</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
              <h5 class="font-bold text-blue-800 mb-2">👶 出生-6个月</h5>
              <ul class="text-sm text-blue-700 space-y-1">
                <li>• 新生儿检查（3-5天）</li>
                <li>• 满月检查</li>
                <li>• 2、4、6个月常规检查</li>
                <li>• 疫苗接种</li>
              </ul>
            </div>

            <div class="p-4 border-2 border-green-200 rounded-lg bg-green-50">
              <h5 class="font-bold text-green-800 mb-2">👶 6-12个月</h5>
              <ul class="text-sm text-green-700 space-y-1">
                <li>• 9个月发育筛查（重要）</li>
                <li>• 12个月一岁检查</li>
                <li>• 首次看牙医</li>
                <li>• 贫血筛查</li>
              </ul>
            </div>

            <div class="p-4 border-2 border-orange-200 rounded-lg bg-orange-50">
              <h5 class="font-bold text-orange-800 mb-2">🚶 12-24个月</h5>
              <ul class="text-sm text-orange-700 space-y-1">
                <li>• 15个月学步期检查</li>
                <li>• 18个月自闭症筛查（重要）</li>
                <li>• 24个月两岁检查</li>
                <li>• 语言发育评估</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <!-- Modal for Illness Details -->
  <div id="illness-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <div id="modal-content" class="p-6">
        <!-- Modal content will be injected here -->
      </div>
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 text-right">
        <button id="modal-close"
          class="bg-[#A57C4F] text-white font-bold py-2 px-4 rounded-lg hover:bg-[#8B6914] transition-colors">关闭</button>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // 数据定义
      const appData = {
        illnessCategories: {
          infection: { title: '🦠 感染性疾病', color: 'blue' },
          fever: { title: '🌡️ 发热相关', color: 'green' },
          digestive: { title: '🤢 消化系统', color: 'yellow' },
          respiratory: { title: '😷 呼吸与五官', color: 'purple' },
          skin: { title: '🩹 皮肤问题', color: 'pink' },
          emergency: { title: '🚨 重要警示', color: 'red' }
        },
        illnesses: [
          // 感染性疾病
          { 
            id: 'hfm', 
            name: '手足口病', 
            icon: '🖐️',
            category: 'infection',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键症状</h4>
              <p class='mb-4 text-gray-700'>发热、咽喉痛，随后口腔内出现疼痛溃疡，手掌、脚底出现不痒的皮疹。宝宝可能会拒绝喝水吃饭，因为嘴巴疼。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>提供凉爽、柔软的食物，比如酸奶、冰淇淋</li>
                <li>鼓励多喝水或口服补液盐，防止脱水</li>
                <li>保持皮疹清洁干燥，居家隔离至少10天</li>
                <li>用温盐水漱口缓解口腔疼痛</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>出现脱水迹象（尿少、哭没眼泪）、持续高热超过3天、精神萎靡或反复惊跳。</p>
            `
          },
          { 
            id: 'roseola', 
            name: '幼儿急疹', 
            icon: '🌹',
            category: 'infection',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键症状</h4>
              <p class='mb-4 text-gray-700'>典型特征是"热退疹出"。先是突然高热3-5天，但宝宝精神还不错。体温恢复正常后，躯干出现粉红色斑丘疹，不痛不痒，1-2天后消退。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>高热期进行物理降温（温水擦浴）和药物降温</li>
                <li>保证充足的液体摄入，预防脱水</li>
                <li>皮疹无需特殊处理，会自然消退</li>
                <li>注意休息，避免去人多的地方</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>3个月以下婴儿发热、发生热性惊厥、持续高热且精神状态很差。</p>
            `
          },
          // 发热相关
          { 
            id: 'viral-fever', 
            name: '病毒性感冒', 
            icon: '🤒',
            category: 'fever',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键症状</h4>
              <p class='mb-4 text-gray-700'>通常起病急，发热（中高热常见）、伴有流涕、鼻塞、咳嗽、咽痛等。全身症状如乏力、肌肉酸痛也较常见。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li><strong>核心是对症支持：</strong>充分休息，保证液体摄入。</li>
                <li><strong>降温：</strong>体温超过38.5℃且宝宝不适时，使用对乙酰氨基酚或布洛芬。</li>
                <li><strong>鼻塞：</strong>用生理盐水滴鼻，配合吸鼻器。</li>
                <li><strong>咳嗽：</strong>保持空气湿润，1岁以上可尝试蜂蜜。</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>3个月以下婴儿发热；精神萎靡；呼吸急促或困难；持续高热不退；出现脱水症状。</p>
            `
          },
          { 
            id: 'febrile-seizure', 
            name: '热性惊厥', 
            icon: '🔥',
            category: 'fever',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键特征</h4>
              <p class='mb-4 text-gray-700'>6个月-5岁宝宝在发热时出现的抽搐，通常持续1-5分钟。看起来很可怕，但大多数是良性的。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 当场处理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>保持冷静，记录惊厥开始时间</li>
                <li>将宝宝侧卧，确保呼吸道通畅</li>
                <li>不要强行按压或往嘴里塞东西</li>
                <li>移除周围尖锐物品，保护头部</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 立即就医</h4>
              <p class='text-gray-700'>首次发生、持续超过5分钟、24小时内反复发作、惊厥后精神状态异常。</p>
            `
          },
          // 消化系统
          { 
            id: 'gastro', 
            name: '病毒性肠胃炎', 
            icon: '🤢',
            category: 'digestive',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键症状</h4>
              <p class='mb-4 text-gray-700'>水样腹泻和呕吐，可能伴有腹痛和低热。宝宝看起来很难受，不想吃东西。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <div class='bg-yellow-50 p-4 rounded-lg mb-4 border-l-4 border-yellow-500'>
                <p class='font-bold text-yellow-800'>首要目标：预防脱水！</p>
              </div>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>使用口服补液盐（ORS）少量多次补充</li>
                <li>继续正常喂养，不要禁食</li>
                <li>逐步恢复正常饮食，选择容易消化的食物</li>
                <li>避免果汁和含糖饮料</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>出现中重度脱水迹象、无法保留任何液体、大便带血、高热或严重腹痛。</p>
            `
          },
          { 
            id: 'constipation', 
            name: '便秘', 
            icon: '🚽',
            category: 'digestive',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 便秘的表现</h4>
              <p class='mb-4 text-gray-700'>排便次数减少、大便干硬、排便困难或疼痛、腹胀不适。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>增加水分摄入，6个月以上可以喝少量水</li>
                <li>添加高纤维食物：梨、李子、豌豆</li>
                <li>腹部按摩：顺时针轻柔按摩</li>
                <li>增加活动量，多爬行或走路</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>超过3天未排便、大便带血、腹痛严重、呕吐或拒食。</p>
            `
          },
          { 
            id: 'colic', 
            name: '肠绞痛', 
            icon: '😭',
            category: 'digestive',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 典型表现</h4>
              <p class='mb-4 text-gray-700'>健康宝宝突然大哭，通常在晚上，哭声尖锐，双腿蜷缩。"3-3-3规律"：每天哭3小时以上，每周3天以上，持续3周以上。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 安抚方法</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>包襁褓：模拟子宫环境，给予安全感</li>
                <li>白噪音：吹风机、洗衣机声音</li>
                <li>轻柔摇摆或走动</li>
                <li>腹部按摩或"飞机抱"姿势</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>哭声异常（尖锐或微弱）、发热、呕吐、腹胀或家长无法承受时。</p>
            `
          },
          // 呼吸与五官
          { 
            id: 'cold', 
            name: '普通感冒', 
            icon: '🤧',
            category: 'respiratory',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键症状</h4>
              <p class='mb-4 text-gray-700'>流鼻涕、鼻塞、打喷嚏。鼻涕可能由清水样变成黄绿色（这不代表细菌感染）。可能伴有轻度咳嗽和低热。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>用生理盐水滴鼻剂和吸鼻器缓解鼻塞</li>
                <li>使用冷雾加湿器，保持空气湿润</li>
                <li>保证充足的液体摄入和休息</li>
                <li>1岁以上可以尝试蜂蜜缓解咳嗽</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>3个月以下婴儿发热、呼吸困难、有耳痛表现、症状超过10天未好转。</p>
            `
          },
          { 
            id: 'cough', 
            name: '咳嗽', 
            icon: '😷',
            category: 'respiratory',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 咳嗽的类型</h4>
              <p class='mb-4 text-gray-700'>干咳、湿咳（有痰）、夜咳。不同类型的咳嗽需要不同的处理方法。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>保持室内空气湿润，使用加湿器</li>
                <li>鼓励多喝温水，稀释痰液</li>
                <li>1岁以上可使用蜂蜜缓解（1茶匙）</li>
                <li>避免烟雾和刺激性气味</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>持续干咳超过2周、咳嗽伴高热、呼吸急促、咳血或绿色痰液。</p>
            `
          },
          { 
            id: 'ear', 
            name: '耳部感染', 
            icon: '👂',
            category: 'respiratory',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 关键症状</h4>
              <p class='mb-4 text-gray-700'>反复抓、拉耳朵；平躺或吃奶时哭闹加剧；发热、烦躁不安。大一点的宝宝会说耳朵疼。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 家庭护理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>关键是疼痛管理：按医嘱使用对乙酰氨基酚或布洛芬</li>
                <li>用温热毛巾敷在耳朵外侧缓解疼痛</li>
                <li>让宝宝多休息，头部稍微抬高</li>
                <li>不要往耳朵里滴任何东西</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>6个月以下婴儿；剧烈耳痛或高热；耳朵有脓性或血性液体流出；症状超过2天未缓解。</p>
            `
          },
          // 皮肤相关
          { 
            id: 'eczema', 
            name: '湿疹', 
            icon: '🩹',
            category: 'skin',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 湿疹表现</h4>
              <p class='mb-4 text-gray-700'>皮肤干燥、发红、瘙痒，常出现在面部、肘窝、膝窝。宝宝会因为痒而抓挠。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 护理要点</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>保持皮肤湿润：使用无香料润肤霜</li>
                <li>避免过热洗澡，水温适中</li>
                <li>选择纯棉、宽松衣物</li>
                <li>剪短指甲，避免抓挠</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>湿疹面积扩大、有感染迹象（流脓、发热）、严重影响睡眠。</p>
            `
          },
          { 
            id: 'diaper-rash', 
            name: '尿布疹', 
            icon: '🍑',
            category: 'skin',
            content: `
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>👀 尿布疹表现</h4>
              <p class='mb-4 text-gray-700'>尿布覆盖区域皮肤发红、疼痛，严重时可能有小疱或破皮。</p>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 护理方法</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li>频繁更换尿布，保持干燥</li>
                <li>用温水轻柔清洁，避免用力擦拭</li>
                <li>涂抹护臀霜形成保护层</li>
                <li>适当让宝宝裸露通风</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 就医指征</h4>
              <p class='text-gray-700'>皮疹扩散到尿布外、出现脓疱、伴有发热或3天未好转。</p>
            `
          },
          // 重要警示
          { 
            id: 'dehydration', 
            name: '脱水预警', 
            icon: '💧',
            category: 'emergency',
            content: `
              <div class='bg-red-50 p-4 rounded-lg mb-4 border-l-4 border-red-500'>
                <p class='font-bold text-red-800'>⚠️ 脱水是很多疾病的共同危险信号！</p>
              </div>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🚨 脱水的警报信号</h4>
              <ul class='list-disc pl-5 mb-4 space-y-2 text-gray-700'>
                <li><strong>尿量减少：</strong>6-8小时没有湿尿布，或尿色深黄</li>
                <li><strong>口干舌燥：</strong>口腔黏膜干燥、发粘</li>
                <li><strong>哭时无泪</strong></li>
                <li><strong>精神状态改变：</strong>异常烦躁，或更危险的萎靡、嗜睡</li>
                <li><strong>体征变化：</strong>眼窝凹陷、前囟门（软点）凹陷</li>
                <li><strong>皮肤弹性差：</strong>轻捏皮肤后回弹缓慢</li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 立即行动</h4>
              <p class='text-gray-700'>如果观察到任何上述迹象，尤其是尿量减少或精神状态改变，<strong>应立即寻求医疗帮助</strong>。在等待就医期间，继续尝试给宝宝补充液体。</p>
            `
          }
        ],
        firstAid: [
          {
            id: 'fever', 
            name: '发热发烧', 
            icon: '🤒',
            content: `
              <h4 class='text-lg font-bold mb-4 text-[#A57C4F]'>🌡️ 如何准确测温</h4>
              <p class='text-gray-700 mb-4'>使用数字体温计。对于3个月以下婴儿，<strong>直肠测温</strong>是唯一推荐的方法。3个月以上可使用直肠、腋下或额温枪。</p>
              <div class='bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-r-lg mb-4'>
                <p class='font-bold mb-2'>🚨 紧急规则：3个月以下婴儿</p>
                <p>直肠温度 ≥ <strong>38°C (100.4°F)</strong> 构成医疗紧急情况。<strong>立即联系医生或去急诊</strong>，不要自行用药。</p>
              </div>
              <h4 class='text-lg font-bold mb-3 text-[#A57C4F]'>🏠 3个月以上儿童的发热管理</h4>
              <ul class='list-disc pl-5 mb-4 space-y-1 text-gray-700'>
                <li><strong>关注舒适度，而非温度数字</strong>——宝宝精神好比温度更重要</li>
                <li>物理降温：穿轻薄衣物、温水擦浴（不要用酒精）</li>
                <li>鼓励多喝水，预防脱水</li>
                <li>当宝宝明显不适时，可按体重计算剂量使用对乙酰氨基酚或布洛芬（6个月以上）</li>
                <li><strong>绝不使用阿司匹林！</strong></li>
              </ul>
              <h4 class='text-lg font-bold mb-3 text-red-600'>🚨 何时联系医生（3个月以上）</h4>
              <ul class='list-disc pl-5 text-gray-700 space-y-1'>
                <li>发热持续超过24小时（2岁以下）或3天（2岁以上）</li>
                <li>体温超过40°C (104°F)</li>
                <li>出现精神萎靡、脖子僵硬、呼吸困难等警报症状</li>
                <li>有热性惊厥史的宝宝再次发热</li>
              </ul>
            `
          },
          {
            id: 'scalds', 
            name: '烫伤处理（热水/蒸汽）', 
            icon: '💧',
            content: `
              <h4 class='text-lg font-bold text-center mb-4 text-[#A57C4F]'>"冲、脱、泡、盖、送"五步法</h4>
              <div class='bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500 mb-4'>
                <h5 class='font-bold text-blue-800 mb-2'>1️⃣ 冲：立即降温</h5>
                <p class='text-blue-700'><strong>立即</strong>用流动的凉爽自来水冲洗伤处至少<strong>15-20分钟</strong>。这是最关键的一步，可以带走热量，减少深层组织损伤。</p>
              </div>
              <div class='bg-green-50 p-4 rounded-lg border-l-4 border-green-500 mb-4'>
                <h5 class='font-bold text-green-800 mb-2'>2️⃣ 脱：移除湿热衣物</h5>
                <p class='text-green-700'>在冲水的同时，<strong>小心地</strong>脱去或剪开被热水浸湿的衣物。<strong>如果衣物粘在皮肤上，切勿强行撕扯</strong>，留给医生处理。</p>
              </div>
              <div class='bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500 mb-4'>
                <h5 class='font-bold text-yellow-800 mb-2'>3️⃣ 泡：持续镇痛</h5>
                <p class='text-yellow-700'>对于小面积烫伤，可在凉水中继续浸泡10-30分钟以缓解疼痛。注意水温，避免宝宝着凉。</p>
              </div>
              <div class='bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500 mb-4'>
                <h5 class='font-bold text-purple-800 mb-2'>4️⃣ 盖：保护创面</h5>
                <p class='text-purple-700'>用干净、无菌的纱布或洁净布料松散覆盖伤口，避免感染。</p>
              </div>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500'>
                <p class='font-bold text-red-800 mb-2'>🚫 绝对禁忌</p>
                <ul class='list-disc pl-5 text-red-700 space-y-1'>
                  <li>禁止用冰块直接敷，可能造成二次冻伤。</li>
                  <li>禁止涂抹牙膏、酱油、药膏等任何未经医生许可的物质。</li>
                  <li>禁止挑破水疱，以免增加感染风险。</li>
                </ul>
              </div>
            `
          },
          {
            id: 'burns', 
            name: '烧伤处理（火焰/热物）', 
            icon: '🔥',
            content: `
              <h4 class='text-lg font-bold text-center mb-4 text-[#A57C4F]'>与烫伤处理类似，但需注意原因</h4>
              <p class='text-gray-600 mb-4'>由火焰、热金属等干热源造成的烧伤，处理原则与烫伤相似，但需更警惕。</p>
              <div class='bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500 mb-4'>
                <h5 class='font-bold text-blue-800 mb-2'>1️⃣ 灭火与隔离</h5>
                <p class='text-blue-700'>如果衣物着火，遵循“停、躺、滚”原则灭火。迅速脱离热源。</p>
              </div>
              <div class='bg-green-50 p-4 rounded-lg border-l-4 border-green-500 mb-4'>
                <h5 class='font-bold text-green-800 mb-2'>2️⃣ 冷却创面</h5>
                <p class='text-green-700'>同样使用流动的凉水冲洗<strong>15-20分钟</strong>。对于化学烧伤（如接触强酸强碱），冲洗时间需要更长，并告知医生化学品种类。</p>
              </div>
              <div class='bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500 mb-4'>
                <h5 class='font-bold text-yellow-800 mb-2'>3️⃣ 移除束缚物</h5>
                <p class='text-yellow-700'>在肿胀发生前，尽快取下戒指、手表、腰带等紧身物品。</p>
              </div>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500'>
                <h5 class='font-bold text-red-800 mb-2'>🚨 立即送医</h5>
                <p class='text-red-700'>所有电烧伤、化学烧伤，以及面积大于宝宝手掌的烧伤，或发生在面部、关节、生殖器等关键部位的烧伤，<strong>必须立即送医</strong>。</p>
              </div>
            `
          },
          {
            id: 'cuts', 
            name: '外伤抓伤', 
            icon: '🩹',
            content: `
              <h4 class='text-lg font-bold mb-4 text-[#A57C4F]'>🔍 首先评估伤势</h4>
              <p class='text-gray-700 mb-4'>查看伤口的深度、长度和出血情况，判断是需要家庭处理还是医疗救治。</p>
              <div class='bg-green-50 p-4 rounded-lg border-l-4 border-green-500 mb-4'>
                <h5 class='font-bold text-green-800 mb-2'>🏠 轻微外伤处理</h5>
                <ul class='list-disc pl-5 text-green-700 space-y-1'>
                  <li>用清水或生理盐水清洗伤口</li>
                  <li>轻压止血，避免用力揉搓</li>
                  <li>涂抹适量抗菌软膏</li>
                  <li>用创可贴或纱布覆盖保护</li>
                </ul>
              </div>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500'>
                <h5 class='font-bold text-red-800 mb-2'>🚨 立即就医指征</h5>
                <ul class='list-disc pl-5 text-red-700 space-y-1'>
                  <li>伤口深度超过皮肤表层，能看到脂肪或肌肉</li>
                  <li>出血不止，持续流血超过10分钟</li>
                  <li>伤口长度超过1厘米或形状不规整</li>
                  <li>被脏物或动物咬伤，需要破伤风疫苗</li>
                  <li>出现感染迹象：红肿热痛、发热、脓液</li>
                </ul>
              </div>
            `
          },
          {
            id: 'swallowing', 
            name: '误吞异物', 
            icon: '😵',
            content: `
              <h4 class='text-lg font-bold mb-4 text-[#A57C4F]'>⚠️ 常见误吞物品</h4>
              <p class='text-gray-700 mb-4'>硬币、纽扣、玩具小零件、电池、磁铁等。误吞后的处理取决于物品的性质和宝宝的症状。</p>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500 mb-4'>
                <h5 class='font-bold text-red-800 mb-2'>🚨 紧急情况（立即急救）</h5>
                <ul class='list-disc pl-5 text-red-700 space-y-1'>
                  <li><strong>尖锐物品：</strong>别针、骨头碎片、玻璃碎片</li>
                  <li><strong>电池：</strong>特别是纽扣电池，可能造成化学灼伤</li>
                  <li><strong>磁铁：</strong>两个或以上磁铁可能在肠道内相吸造成穿孔</li>
                  <li><strong>大物品：</strong>可能造成肠梗阻的物品</li>
                </ul>
              </div>
              <div class='bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500 mb-4'>
                <h5 class='font-bold text-yellow-800 mb-2'>⏰ 观察处理（密切监护）</h5>
                <ul class='list-disc pl-5 text-yellow-700 space-y-1'>
                  <li><strong>小硬币、纽扣：</strong>多数会自然排出，观察72小时</li>
                  <li>检查每次大便，看是否有异物排出</li>
                  <li>正常饮食，多吃纤维食物促进排便</li>
                  <li>避免使用泻药或强行催吐</li>
                </ul>
              </div>
              <div class='bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500'>
                <h5 class='font-bold text-blue-800 mb-2'>📞 何时联系医生</h5>
                <ul class='list-disc pl-5 text-blue-700 space-y-1'>
                  <li>宝宝出现呕吐、腹痛、拒食等症状</li>
                  <li>72小时后未在大便中发现异物</li>
                  <li>任何电池、磁铁或尖锐物品的误吞</li>
                  <li>宝宝行为异常或精神状态改变</li>
                </ul>
              </div>
            `
          },
          {
            id: 'choking', 
            name: '窒息急救', 
            icon: '😰',
            content: `
              <h4 class='text-lg font-bold mb-4 text-[#A57C4F]'>🚨 窒息的紧急识别</h4>
              <p class='text-gray-700 mb-4'>宝宝无法哭泣、说话或咳嗽，面部发紫，用手抓挠喉咙，或发出高音调喘息声。</p>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500 mb-4'>
                <h5 class='font-bold text-red-800 mb-2'>👶 1岁以下婴儿急救法</h5>
                <ol class='list-decimal pl-5 text-red-700 space-y-2'>
                  <li><strong>背部拍击：</strong>婴儿脸朝下放在前臂上，头部低于胸部，用手掌跟快速拍击肩胛骨之间5次</li>
                  <li><strong>胸部推压：</strong>翻转婴儿，用两根手指在胸骨下1/3处快速推压5次</li>
                  <li><strong>重复以上步骤</strong>直到异物排出或婴儿失去反应</li>
                  <li>如失去反应，开始CPR并立即呼叫急救</li>
                </ol>
              </div>
              <div class='bg-orange-50 p-4 rounded-lg border-l-4 border-orange-500 mb-4'>
                <h5 class='font-bold text-orange-800 mb-2'>🧒 1岁以上儿童急救法（海姆利克急救法）</h5>
                <ol class='list-decimal pl-5 text-orange-700 space-y-2'>
                  <li><strong>站位：</strong>站在孩子身后，双臂环抱其腰部</li>
                  <li><strong>手势：</strong>一手握拳，拳心向内置于肚脐上方，另一手覆盖</li>
                  <li><strong>推压：</strong>快速向内上方推压，连续5-10次</li>
                  <li><strong>检查：</strong>检查口腔，如看到异物可用手指扫出</li>
                </ol>
              </div>
              <div class='bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500'>
                <p class='font-bold text-blue-800 mb-2'>⚠️ 重要提醒</p>
                <ul class='list-disc pl-5 text-blue-700 space-y-1'>
                  <li>如果宝宝还能咳嗽或哭泣，<strong>不要拍背</strong>，鼓励其自行咳出</li>
                  <li>切勿盲目用手指掏异物，可能将其推向更深处</li>
                  <li>即使异物已排出，如果使用过急救措施，仍需就医检查</li>
                  <li>建议所有家长学习正规的CPR和急救课程</li>
                </ul>
              </div>
            `
          },
          {
            id: 'poisoning', 
            name: '中毒处理', 
            icon: '☠️',
            content: `
              <h4 class='text-lg font-bold mb-4 text-[#A57C4F]'>🧪 常见中毒源</h4>
              <p class='text-gray-700 mb-4'>家用清洁剂、药物、化妆品、植物、食物等。快速识别中毒源和正确的初步处理至关重要。</p>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500 mb-4'>
                <h5 class='font-bold text-red-800 mb-2'>🚨 立即行动步骤</h5>
                <ol class='list-decimal pl-5 text-red-700 space-y-2'>
                  <li><strong>确保安全：</strong>移除宝宝远离中毒源，确保自身安全</li>
                  <li><strong>立即呼叫：</strong>拨打120急救或中毒控制中心热线</li>
                  <li><strong>收集信息：</strong>保留毒物包装，记录摄入时间和数量</li>
                  <li><strong>保持冷静：</strong>安抚宝宝，密切观察症状变化</li>
                </ol>
              </div>
              <div class='bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500 mb-4'>
                <h5 class='font-bold text-yellow-800 mb-2'>📋 不同中毒的处理原则</h5>
                <ul class='list-disc pl-5 text-yellow-700 space-y-2'>
                  <li><strong>口服中毒：</strong>如果宝宝清醒且无腐蚀性中毒，可给少量清水稀释。<strong>切勿催吐</strong>除非医生指示</li>
                  <li><strong>皮肤接触：</strong>立即用大量清水冲洗至少15分钟，脱去污染衣物</li>
                  <li><strong>眼部接触：</strong>用流动清水冲洗至少15分钟，眼睑要撑开</li>
                  <li><strong>吸入中毒：</strong>立即转移到新鲜空气处，如有呼吸困难立即就医</li>
                </ul>
              </div>
              <div class='bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500 mb-4'>
                <h5 class='font-bold text-blue-800 mb-2'>⚠️ 中毒症状识别</h5>
                <ul class='list-disc pl-5 text-blue-700 space-y-1'>
                  <li>恶心、呕吐、腹痛、腹泻</li>
                  <li>头晕、嗜睡、意识改变</li>
                  <li>呼吸困难、皮肤颜色改变</li>
                  <li>口腔周围灼伤、异常气味</li>
                </ul>
              </div>
              <div class='bg-red-50 p-4 rounded-lg border-l-4 border-red-500'>
                <p class='font-bold text-red-800 mb-2'>🚫 绝对禁忌</p>
                <ul class='list-disc pl-5 text-red-700 space-y-1'>
                  <li>不要催吐，除非毒物控制中心或医生明确指示</li>
                  <li>不要给予牛奶或其他液体，除非专业人员指导</li>
                  <li>不要使用"万能解毒剂"或网络偏方</li>
                  <li>不要等待症状出现才寻求帮助</li>
                </ul>
              </div>
            `
          }
        ]
      };

      // 导航功能
      const navItems = [
        { id: 'common_illnesses', text: '常见疾病', icon: '🩺' },
        { id: 'first_aid', text: '发烧与急救', icon: '⛑️' },
        { id: 'vaccination', text: '疫苗接种', icon: '💉' },
        { id: 'health_checks', text: '健康检查', icon: '🗓️' }
      ];

      const mainNav = document.getElementById('main-nav');
      navItems.forEach((item, index) => {
        const navLink = document.createElement('a');
        navLink.href = '#';
        navLink.className = `nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer ${index === 0 ? 'active' : ''}`;
        navLink.innerHTML = `<span class="mr-3">${item.icon}</span><span class="hidden md:inline">${item.text}</span><span class="md:hidden">${item.text}</span>`;
        navLink.onclick = (e) => {
          e.preventDefault();
          showSection(item.id);
          document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
          navLink.classList.add('active');
        };
        mainNav.appendChild(navLink);
      });

      function showSection(sectionId) {
        document.querySelectorAll('main > section').forEach(section => {
          section.classList.add('hidden');
        });
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
          targetSection.classList.remove('hidden');
        }
      }

      // 常见疾病卡片和模态框
      const illnessCategoriesContainer = document.getElementById('illness-categories');
      const modal = document.getElementById('illness-modal');
      const modalContent = document.getElementById('modal-content');
      const modalClose = document.getElementById('modal-close');

      // Render illness categories and cards
      for (const categoryId in appData.illnessCategories) {
        const category = appData.illnessCategories[categoryId];
        const categoryWrapper = document.createElement('div');
        categoryWrapper.id = categoryId;
        categoryWrapper.innerHTML = `<h3 class="text-2xl font-bold text-[#A57C4F] mb-4 mt-8">${category.title}</h3>`;
        
        const grid = document.createElement('div');
        grid.className = 'grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4';

        const illnessesInCategory = appData.illnesses.filter(ill => ill.category === categoryId);
        illnessesInCategory.forEach(illness => {
          const card = document.createElement('div');
          card.className = 'bg-white p-4 rounded-lg shadow-md card-hover flex flex-col items-center justify-center text-center cursor-pointer';
          card.innerHTML = `
            <div class="text-4xl mb-2">${illness.icon}</div>
            <h4 class="font-bold text-[#A57C4F] text-sm">${illness.name}</h4>
          `;
          card.addEventListener('click', () => {
            modalContent.innerHTML = `
              <h3 class="text-2xl font-bold mb-4 text-[#A57C4F]">${illness.icon} ${illness.name}</h3>
              <div class="prose max-w-none">${illness.content}</div>
            `;
            modal.classList.remove('hidden');
            modal.classList.add('flex');
          });
          grid.appendChild(card);
        });
        categoryWrapper.appendChild(grid);
        illnessCategoriesContainer.appendChild(categoryWrapper);
      }

      modalClose.addEventListener('click', () => {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
      });
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
          modal.classList.remove('flex');
        }
      });

      // 紧急急救卡片
      const aidGrid = document.getElementById('aid-grid');
      appData.firstAid.forEach(aid => {
        const card = document.createElement('div');
        card.className = 'bg-white p-6 rounded-lg shadow-md card-hover';
        card.innerHTML = `
          <h3 class="text-xl font-bold mb-6 text-[#A57C4F]">${aid.icon} ${aid.name}</h3>
          <div>${aid.content}</div>
        `;
        aidGrid.appendChild(card);
      });

      // 疫苗图表
      if (typeof Chart !== 'undefined') {
        const ctx = document.getElementById('vaccineChart');
        if (ctx) {
          new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
              labels: ['出生', '2个月', '4个月', '6个月', '12个月'],
              datasets: [{
                label: '疫苗接种',
                data: [2, 3, 3, 3, 4],
                backgroundColor: '#A57C4F',
                borderColor: '#8B6914',
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: { legend: { display: false } },
              scales: { y: { beginAtZero: true, max: 5 } }
            }
          });
        }
      }
      
      // 初始化显示
      showSection('common_illnesses');
    });
  </script>
</body>

</html>