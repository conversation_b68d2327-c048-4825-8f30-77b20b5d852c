<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>配方奶喂养新手指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <!-- Chosen Palette: Calm Harmony -->
  <!-- Application Structure Plan: 我设计了一个任务导向的仪表板式单页应用。顶部的快速导航让用户能立即跳转到最关心的部分，如"常见问题速查"或"冲泡指南"。这种结构优先考虑了新手妈妈在实际场景中（如半夜安抚哭闹的宝宝）的即时需求，而非强迫她们按顺序阅读。核心交互包括：1. 问题速查手风琴（快速定位问题和解决方案）；2. 月龄选择器驱动的喂养量图表（将静态数据变为个性化工具）；3. 奶粉类型切换卡片（简化比较过程）。这个设计旨在将信息从"被动阅读"转变为"主动查询"，提供一个实用、减压的数字伴侣。 -->
  <!-- Visualization & Content Choices: 1. 喂养量表 -> 交互式条形图 (Chart.js): 目标是告知。用户通过月龄滑块或按钮互动，直观看到对应奶量，比查表更友好。 2. 奶粉形态对比 -> 选项卡式卡片 (HTML/JS): 目标是比较。一次展示一种，避免信息过载，简化决策。 3. 常见问题表 -> 手风琴式列表 (HTML/JS): 目标是组织和快速查找。默认折叠，保持界面整洁，用户按需展开，直达解决方案。 4. 冲泡/卫生步骤 -> 图标化流程图 (HTML/Tailwind): 目标是指导。视觉化的步骤比纯文本更易遵循，降低操作错误风险。 5. 饥饿/饱腹信号 -> 双栏对比卡片 (HTML/Tailwind): 目标是对比和学习。并列展示帮助用户快速区分宝宝的细微信号。所有实现均使用Canvas或纯HTML/CSS，无SVG/Mermaid。 -->
  <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }

    .accordion-button.open .accordion-arrow {
      transform: rotate(180deg);
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }

    .chart-container {
      position: relative;
      width: 100%;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      height: 300px;
    }

    @media (min-width: 640px) {
      .chart-container {
        height: 350px;
      }
    }

    .tab-button.active {
      background-color: #A57C4F;
      color: #FFFFFF;
    }
  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button - positioned at top left -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">配方奶喂养</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 喂养方法 -->
      <section id="feeding_method" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">配方奶喂养方法</h2>

        <!-- 1. 冲泡技巧（最重要，放在最前面） -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">1. 正确冲泡步骤</h3>
          
          <div class="warning-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">⚠️ 重要提醒</h4>
            <p class="text-lg mb-4">严格按照奶粉包装上的比例冲调，<strong>绝不可随意增减浓度</strong>！过浓可能造成便秘、肾脏负担；过稀会营养不足。</p>
          </div>

          <h4 class="text-xl font-bold text-[#A57C4F] mb-4">安全冲泡七步法</h4>
          
          <div class="mb-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">1</div>
                <h6 class="font-bold text-sm mb-1">清洁双手</h6>
                <p class="text-xs text-gray-600">用肥皂洗手20秒，彻底清洁</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">2</div>
                <h6 class="font-bold text-sm mb-1">消毒器具</h6>
                <p class="text-xs text-gray-600">奶瓶、奶嘴沸水煮5分钟</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">3</div>
                <h6 class="font-bold text-sm mb-1">准备温水</h6>
                <p class="text-xs text-gray-600">40-50°C温开水，先放水</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">4</div>
                <h6 class="font-bold text-sm mb-1">加入奶粉</h6>
                <p class="text-xs text-gray-600">按比例添加，用专用勺</p>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-4">
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">5</div>
                <h6 class="font-bold text-sm mb-1">轻柔摇匀</h6>
                <p class="text-xs text-gray-600">水平转动，不要上下剧烈摇晃</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">6</div>
                <h6 class="font-bold text-sm mb-1">测试温度</h6>
                <p class="text-xs text-gray-600">滴在手腕内侧，感觉温热</p>
              </div>
              <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-white bg-[#A57C4F] w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2">7</div>
                <h6 class="font-bold text-sm mb-1">立即喂哺</h6>
                <p class="text-xs text-gray-600">冲好后2小时内使用</p>
              </div>
            </div>
          </div>

          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h5 class="font-bold text-blue-800 mb-2">💡 冲泡小贴士：</h5>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• 先放水再放奶粉，避免结块</li>
              <li>• 奶粉勺要干燥，刮平不压实</li>
              <li>• 水温太高会破坏营养成分</li>
              <li>• 摇匀后静置1分钟让泡沫消散</li>
            </ul>
          </div>
        </div>

        <!-- 2. 喂养量和频率 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">2. 喂养量和频率</h3>
          
          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">📏 按需喂养原则</h4>
            <p class="text-lg mb-4">下面的数据是<strong>参考平均值</strong>，每个宝宝都不同。重要的是观察宝宝的饥饿和饱足信号。</p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-xl font-bold text-[#A57C4F] mb-4">分月龄喂养参考</h4>
              <p class="text-sm text-gray-500 mb-4">点击下方按钮查看不同月龄的喂养量。</p>
              <div class="flex flex-wrap justify-center gap-2 mb-4">
                <button class="age-button bg-gray-200 hover:bg-gray-300 text-gray-800 text-xs font-bold py-1 px-2 rounded-full" data-age="0">新生儿</button>
                <button class="age-button bg-gray-200 hover:bg-gray-300 text-gray-800 text-xs font-bold py-1 px-2 rounded-full" data-age="1">1-2个月</button>
                <button class="age-button bg-gray-200 hover:bg-gray-300 text-gray-800 text-xs font-bold py-1 px-2 rounded-full" data-age="2">2-4个月</button>
                <button class="age-button bg-gray-200 hover:bg-gray-300 text-gray-800 text-xs font-bold py-1 px-2 rounded-full" data-age="3">4-6个月</button>
                <button class="age-button bg-gray-200 hover:bg-gray-300 text-gray-800 text-xs font-bold py-1 px-2 rounded-full" data-age="4">6-12个月</button>
              </div>
              <div class="chart-container">
                <canvas id="feedingChart"></canvas>
              </div>
            </div>
            <div>
              <h4 class="text-xl font-bold text-[#8FBC8F] mb-4">识别宝宝信号</h4>
              <div class="grid grid-cols-1 gap-4">
                <div>
                  <h5 class="font-semibold text-green-700 mb-2">🍼 饿了的信号</h5>
                  <ul class="space-y-1 text-sm text-gray-700">
                    <li>• 咂嘴、舔嘴唇</li>
                    <li>• 吸吮小手或衣物</li>
                    <li>• 头转向一侧张嘴（觅食）</li>
                    <li>• 烦躁、扭动</li>
                    <li class="text-red-600">• 哭闹（已经很饿了）</li>
                  </ul>
                </div>
                <div>
                  <h5 class="font-semibold text-blue-700 mb-2">😊 吃饱的信号</h5>
                  <ul class="space-y-1 text-sm text-gray-700">
                    <li>• 闭嘴或转开头</li>
                    <li>• 用手推开奶瓶</li>
                    <li>• 吸吮变慢或停止</li>
                    <li>• 身体和手掌放松</li>
                    <li>• 昏昏欲睡</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 3. 正确喂养姿势 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">3. 正确喂养姿势</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-xl font-bold mb-4">基本姿势要点</h4>
              <ul class="space-y-3 text-gray-700">
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>宝宝呈半竖直状态，头高于胃部</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>奶瓶保持水平，让宝宝主动吸吮</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>奶嘴完全充满奶液，避免吸入空气</li>
                <li class="flex items-start"><span class="text-green-500 mr-2">✓</span>喂奶中途和结束后都要拍嗝</li>
                <li class="text-red-600 font-bold">❌ 严禁支撑奶瓶或让宝宝含着睡觉</li>
              </ul>
            </div>
            <div>
              <h4 class="text-xl font-bold mb-4">拍嗝技巧</h4>
              <div class="space-y-3">
                <div class="p-3 bg-gray-50 rounded-lg">
                  <h5 class="font-bold text-sm">直立拍嗝</h5>
                  <p class="text-xs text-gray-600">宝宝趴在肩膀上，轻拍后背</p>
                </div>
                <div class="p-3 bg-gray-50 rounded-lg">
                  <h5 class="font-bold text-sm">坐式拍嗝</h5>
                  <p class="text-xs text-gray-600">宝宝坐在膝盖上，支撑下巴</p>
                </div>
                <div class="p-3 bg-gray-50 rounded-lg">
                  <h5 class="font-bold text-sm">俯卧拍嗝</h5>
                  <p class="text-xs text-gray-600">宝宝趴在大腿上，轻抚后背</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 奶粉选择 -->
      <section id="formula_choice" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">奶粉选择</h2>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">营养成分选择要点</h3>
          
          <div class="highlight-box p-6 rounded-lg mb-6">
            <h4 class="text-xl font-bold mb-4">🧬 核心营养原则</h4>
            <p class="text-lg mb-4">选择配方奶粉时，<strong>营养成分的质量比品牌更重要</strong>。以下成分是宝宝健康成长的关键。</p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 必需营养成分 -->
            <div class="space-y-4">
              <h4 class="text-xl font-bold text-[#A57C4F] mb-4">🔬 必需营养成分</h4>
              
              <div class="p-4 border-2 border-green-200 rounded-lg bg-green-50">
                <h5 class="font-bold text-green-800 mb-2">铁元素 (必须强化)</h5>
                <p class="text-sm text-green-700 mb-2">• 含量：4-12mg/100g奶粉</p>
                <p class="text-sm text-green-700">• 作用：预防缺铁性贫血，支持大脑发育</p>
                <p class="text-xs text-red-600 mt-1">⚠️ 法律要求必须强化，选择时优先关注</p>
              </div>

              <div class="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
                <h5 class="font-bold text-blue-800 mb-2">DHA & ARA</h5>
                <p class="text-sm text-blue-700 mb-2">• DHA：支持大脑和视力发育</p>
                <p class="text-sm text-blue-700 mb-2">• ARA：促进大脑和免疫系统发育</p>
                <p class="text-xs text-blue-600 mt-1">💡 选择同时含有DHA和ARA的配方</p>
              </div>

              <div class="p-4 border-2 border-purple-200 rounded-lg bg-purple-50">
                <h5 class="font-bold text-purple-800 mb-2">维生素D</h5>
                <p class="text-sm text-purple-700 mb-2">• 含量：40-100IU/100mL冲调液</p>
                <p class="text-sm text-purple-700">• 作用：促进钙吸收，骨骼发育</p>
              </div>

              <div class="p-4 border-2 border-orange-200 rounded-lg bg-orange-50">
                <h5 class="font-bold text-orange-800 mb-2">益生元 (可选)</h5>
                <p class="text-sm text-orange-700 mb-2">• 如：低聚果糖(FOS)、低聚半乳糖(GOS)</p>
                <p class="text-sm text-orange-700">• 作用：促进肠道健康，增强免疫力</p>
              </div>
            </div>

            <!-- 蛋白质结构选择 -->
            <div class="space-y-4">
              <h4 class="text-xl font-bold text-[#A57C4F] mb-4">🥛 蛋白质结构选择</h4>
              
              <div class="p-4 border-2 border-gray-200 rounded-lg">
                <h5 class="font-bold text-gray-800 mb-2">标准配方</h5>
                <p class="text-sm text-gray-700 mb-2">• 完整牛奶蛋白</p>
                <p class="text-sm text-gray-700 mb-2">• 适合：大部分健康宝宝</p>
                <p class="text-xs text-green-600">✓ 首选，营养全面价格适中</p>
              </div>

              <div class="p-4 border-2 border-yellow-200 rounded-lg bg-yellow-50">
                <h5 class="font-bold text-yellow-800 mb-2">部分水解配方</h5>
                <p class="text-sm text-yellow-700 mb-2">• 蛋白质预先分解成小分子</p>
                <p class="text-sm text-yellow-700 mb-2">• 适合：有过敏家族史的宝宝</p>
                <p class="text-xs text-yellow-600">🔍 需要医生建议使用</p>
              </div>

              <div class="p-4 border-2 border-red-200 rounded-lg bg-red-50">
                <h5 class="font-bold text-red-800 mb-2">深度水解/氨基酸配方</h5>
                <p class="text-sm text-red-700 mb-2">• 蛋白质完全分解或用氨基酸替代</p>
                <p class="text-sm text-red-700 mb-2">• 适合：确诊牛奶过敏的宝宝</p>
                <p class="text-xs text-red-600">⚠️ 必须医生处方使用</p>
              </div>

              <div class="warning-box p-4 rounded-lg mt-4">
                <h5 class="font-bold text-red-800 mb-2">🚫 避免的成分</h5>
                <ul class="text-sm text-red-700 space-y-1">
                  <li>• 蔗糖、果糖（增加龋齿风险）</li>
                  <li>• 人工色素和香精</li>
                  <li>• 过量的维生素A（可能中毒）</li>
                  <li>• 未标明来源的"天然香料"</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-2xl font-bold text-[#A57C4F] mb-6">中国市场选购要点</h3>
          <div class="space-y-4">
            <div class="highlight-box p-4 rounded-lg">
              <h4 class="text-lg font-bold mb-2">🇨🇳 认准配方注册号</h4>
              <p class="text-sm">所有合法销售的婴幼儿配方奶粉都必须有"国食注字YPxxxxxxxx"格式的注册号，这是安全保障的第一步。</p>
            </div>
            <div class="p-4 border-2 border-gray-200 rounded-lg">
              <h4 class="text-lg font-bold mb-3">📊 奶粉分段说明</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-3 bg-yellow-50 rounded">
                  <h5 class="font-bold text-yellow-800">1段 (0-6个月)</h5>
                  <p class="text-xs text-yellow-700 mt-1">最接近母乳，营养最全面</p>
                </div>
                <div class="text-center p-3 bg-green-50 rounded">
                  <h5 class="font-bold text-green-800">2段 (6-12个月)</h5>
                  <p class="text-xs text-green-700 mt-1">配合辅食，增强铁锌含量</p>
                </div>
                <div class="text-center p-3 bg-blue-50 rounded">
                  <h5 class="font-bold text-blue-800">3段 (12-36个月)</h5>
                  <p class="text-xs text-blue-700 mt-1">均衡营养，支持成长</p>
                </div>
              </div>
            </div>
            <div class="warning-box p-4 rounded-lg">
              <h4 class="text-lg font-bold mb-2">⚠️ 重要提醒</h4>
              <ul class="text-sm space-y-1">
                <li>• 优先选择正规渠道的"国行版"产品</li>
                <li>• 必须选择强化铁的配方，预防贫血</li>
                <li>• 禁止自制配方奶，极其危险</li>
                <li>• 1岁前不可用普通牛奶替代</li>
                <li>• 特殊配方需医生指导使用</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- 其它问题 -->
      <section id="other_problems" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">其它问题</h2>

        <div class="highlight-box p-6 rounded-lg">
          <h3 class="text-xl font-bold mb-4">💡 解决问题的原则</h3>
          <p class="text-lg">配方奶喂养的大部分问题都与<strong>冲调方法、喂养姿势或奶嘴选择</strong>有关。遇到问题先检查这三方面！</p>
        </div>

        <div id="accordion-container" class="space-y-3">
          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>宝宝吐奶怎么办？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>预防方法：</strong><br>
                1. 确保奶嘴孔大小适中 - 倒转奶瓶时奶液应缓慢滴出<br>
                2. 喂奶时保持宝宝半竖直状态<br>
                3. 每喂50-60ml就暂停拍嗝<br>
                4. 喂完后竖抱15-30分钟<br>
                <strong class="text-red-600">需要就医：</strong>喷射状呕吐、体重不增、频繁呕吐
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>宝宝便秘怎么办？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>首先检查：</strong><br>
                1. 奶粉冲调比例是否正确 - 过浓容易便秘<br>
                2. 每天喂奶量是否充足<br>
                3. 宝宝活动量是否足够<br>
                <strong>缓解方法：</strong><br>
                • 腹部顺时针按摩<br>
                • 做蹬腿运动<br>
                • 6个月后可适量添加水果泥<br>
                <strong class="text-red-600">注意：</strong>不要随意给6个月以下宝宝喂水
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>宝宝胀气哭闹？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>预防胀气：</strong><br>
                1. 选择防胀气奶嘴和奶瓶<br>
                2. 确保奶嘴始终充满奶液<br>
                3. 喂奶过程中多次拍嗝<br>
                4. 避免宝宝过度饥饿时匆忙进食<br>
                <strong>安抚技巧：</strong><br>
                • 飞机抱（让宝宝趴在前臂上）<br>
                • 温柔的腹部按摩<br>
                • 轻声安抚或播放白噪音
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>怀疑宝宝过敏？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>常见过敏症状：</strong><br>
                • 皮肤：湿疹、荨麻疹、红疹<br>
                • 消化：频繁呕吐、腹泻、便血、拒奶<br>
                • 呼吸：咳嗽、喘息、鼻塞<br>
                • 行为：异常哭闹、睡眠不安<br>
                <strong class="text-red-600">重要：</strong><br>
                • 不要自行更换特殊配方奶<br>
                • 出现疑似过敏症状立即就医<br>
                • 记录症状时间和严重程度<br>
                • 按医生建议选择低敏配方
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>宝宝不爱喝奶怎么办？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong>检查原因：</strong><br>
                1. 奶嘴流速是否合适 - 太快会呛到，太慢会累<br>
                2. 奶液温度是否适宜<br>
                3. 宝宝是否生病不适<br>
                4. 环境是否安静舒适<br>
                <strong>改善方法：</strong><br>
                • 尝试不同品牌的奶嘴<br>
                • 调整喂奶环境和时间<br>
                • 增加肌肤接触建立安全感<br>
                • 少量多餐，不强迫进食
              </div>
            </div>
          </div>

          <div class="border-2 border-gray-200 rounded-lg bg-white overflow-hidden hover:border-[#A57C4F] transition">
            <button class="accordion-button w-full text-left p-6 flex justify-between items-center font-bold text-lg text-[#434242] focus:outline-none">
              <span>什么时候需要看医生？</span>
              <span class="accordion-arrow text-[#A57C4F] transition-transform duration-300 text-2xl">▼</span>
            </button>
            <div class="accordion-content">
              <div class="p-6 border-t-2 border-gray-100 text-gray-700 text-lg leading-relaxed">
                <strong class="text-red-600">立即就医情况：</strong><br>
                • 宝宝发热、呼吸困难<br>
                • 严重腹泻、便血<br>
                • 喷射状呕吐<br>
                • 超过6小时不排尿<br>
                • 明显脱水（哭时无眼泪、嘴唇干燥）<br>
                • 体重不增加或下降<br>
                <strong>预约咨询：</strong><br>
                • 持续的喂养困难<br>
                • 怀疑过敏或不耐受<br>
                • 成长发育担忧
              </div>
            </div>
          </div>
        </div>
      </section>

    </main>
  </div>

  <script>
    const sections = [
      { id: 'feeding_method', title: '喂养方法', icon: '🍼' },
      { id: 'formula_choice', title: '奶粉选择', icon: '🥫' },
      { id: 'other_problems', title: '其它问题', icon: '🛠️' }
    ];

    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

        window.scrollTo(0, 0);

        if (targetId === 'other_problems') {
          setupAccordion();
        }
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 处理URL hash跳转
      function handleHashChange() {
        const hash = window.location.hash.substring(1); // 去掉#符号
        if (hash && sections.find(s => s.id === hash)) {
          activateSection(hash);
        }
      }

      // 页面加载时检查hash
      const initialHash = window.location.hash.substring(1);
      if (initialHash && sections.find(s => s.id === initialHash)) {
        activateSection(initialHash);
      } else {
        // 默认激活第一个标签
        activateSection('feeding_method');
      }

      // 监听hash变化（支持浏览器前进后退）
      window.addEventListener('hashchange', handleHashChange);



      // 图表数据
      const feedingData = {
        labels: ['每次奶量 (mL)', '每日次数'],
        datasets: [
          { age: 0, label: '新生儿 (0-1个月)', data: [60, 8], backgroundColor: 'rgba(165, 124, 79, 0.6)', borderColor: '#A57C4F', borderWidth: 2 },
          { age: 1, label: '1-2 个月', data: [90, 7], backgroundColor: 'rgba(129, 178, 154, 0.6)', borderColor: '#81B29A', borderWidth: 2 },
          { age: 2, label: '2-4 个月', data: [120, 6], backgroundColor: 'rgba(242, 204, 143, 0.6)', borderColor: '#F2CC8F', borderWidth: 2 },
          { age: 3, label: '4-6 个月', data: [150, 5], backgroundColor: 'rgba(234, 205, 164, 0.6)', borderColor: '#EACDA4', borderWidth: 2 },
          { age: 4, label: '6-12 个月', data: [180, 4], backgroundColor: 'rgba(143, 188, 143, 0.6)', borderColor: '#8FBC8F', borderWidth: 2 },
        ]
      };

      const ctx = document.getElementById('feedingChart').getContext('2d');
      const feedingChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: feedingData.labels,
          datasets: [feedingData.datasets[0]]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '数值'
              }
            }
          },
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            tooltip: {
              callbacks: {
                label: function (context) {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ';
                  }
                  if (context.parsed.y !== null) {
                    if (context.label === '每次奶量 (mL)') {
                      label += context.parsed.y + ' mL';
                    } else {
                      label += context.parsed.y + ' 次';
                    }
                  }
                  return label;
                }
              }
            }
          }
        }
      });

      window.updateChart = (ageIndex) => {
        feedingChart.data.datasets = [feedingData.datasets[ageIndex]];
        feedingChart.update();

        // 更新按钮样式
        const ageButtons = document.querySelectorAll('.age-button');
        ageButtons.forEach((btn, index) => {
          if (index === ageIndex) {
            btn.style.backgroundColor = '#A57C4F';
            btn.style.color = 'white';
          } else {
            btn.style.backgroundColor = '#E5E7EB';
            btn.style.color = '#1F2937';
          }
        });
      };

      document.querySelectorAll('.age-button').forEach(button => {
        button.addEventListener('click', (e) => {
          const ageIndex = parseInt(e.target.dataset.age);
          updateChart(ageIndex);
        });
      });

      // 初始化图表
      updateChart(0);

      // 手风琴功能
      function setupAccordion() {
        const accordionContainer = document.getElementById('accordion-container');
        if (!accordionContainer) return;

        accordionContainer.addEventListener('click', function (e) {
          const button = e.target.closest('.accordion-button');
          if (button) {
            const content = button.nextElementSibling;
            const isOpen = button.classList.contains('open');

            // 关闭所有其他的
            accordionContainer.querySelectorAll('.accordion-button').forEach(btn => {
              btn.classList.remove('open');
              btn.nextElementSibling.style.maxHeight = null;
            });

            // 切换当前的
            if (!isOpen) {
              button.classList.add('open');
              content.style.maxHeight = content.scrollHeight + "px";
            }
          }
        });
      }
    });
  </script>
</body>

</html>