<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>宝宝发育里程碑指南</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background-color: #FFFBF5;
      color: #434242;
    }

    .nav-item.active {
      background-color: #EACDA4;
      color: #434242;
      font-weight: 700;
    }

    .highlight-box {
      border-left: 4px solid #8FBC8F;
      background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    }

    .warning-box {
      border-left: 4px solid #D99477;
      background: linear-gradient(135deg, #fdf2f0 0%, #f9e8e3 100%);
    }

    .chart-container {
      position: relative;
      width: 100%;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      height: 400px;
    }

    @media (min-width: 640px) {
      .chart-container {
        height: 450px;
      }
    }

    .milestone-card {
      background-color: #ffffff;
      border: 1px solid #fde68a;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .milestone-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    }



    .fade-in {
      animation: fadeIn 0.8s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>

<body>
  <div class="flex flex-col md:flex-row min-h-screen">
    <aside class="w-full md:w-64 bg-[#A57C4F] text-white">
      <!-- Back to Home Button -->
      <div class="p-4">
        <a href="/wiki"
          class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-sm">
          返回首页
        </a>
      </div>
      
      <div class="p-6 text-center">
        <h1 class="text-2xl font-bold">宝宝发育</h1>
      </div>
      <nav id="main-nav" class="flex flex-row md:flex-col p-4 justify-center md:justify-start flex-wrap"></nav>
    </aside>

    <main class="flex-1 p-6 md:p-10">
      <!-- 身体发育指标 -->
      <section id="physical_growth" class="space-y-6">
        <h2 class="text-3xl font-bold text-[#A57C4F]">身体发育指标</h2>
        
        <div class="highlight-box p-6 rounded-lg mb-6">
          <h3 class="text-xl font-bold mb-4">📏 WHO 标准生长曲线</h3>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="flex justify-center items-center gap-4 mb-4">
            <div class="flex rounded-md shadow-sm">
              <button id="btn-boy"
                class="gender-btn relative inline-flex items-center rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">男宝宝</button>
              <button id="btn-girl"
                class="gender-btn relative -ml-px inline-flex items-center rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">女宝宝</button>
            </div>
            <div class="flex rounded-md shadow-sm">
              <button id="btn-weight"
                class="metric-btn relative inline-flex items-center rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">体重 (kg)</button>
              <button id="btn-height"
                class="metric-btn relative -ml-px inline-flex items-center rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">身长 (cm)</button>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="growthChart"></canvas>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="milestone-card">
            <h3 class="text-xl font-bold text-[#A57C4F] mb-4">📊 正常体重范围（参考值）</h3>
            <div class="space-y-3 text-sm">
              <div class="p-3 bg-green-50 rounded-lg">
                <h4 class="font-bold text-green-800">出生 - 3个月</h4>
                <p class="text-green-700">男宝: 2.5-7.1kg，女宝: 2.4-6.6kg</p>
              </div>
              <div class="p-3 bg-blue-50 rounded-lg">
                <h4 class="font-bold text-blue-800">4-6个月</h4>
                <p class="text-blue-700">男宝: 5.6-9.5kg，女宝: 5.2-8.8kg</p>
              </div>
              <div class="p-3 bg-purple-50 rounded-lg">
                <h4 class="font-bold text-purple-800">7-12个月</h4>
                <p class="text-purple-700">男宝: 6.5-12.4kg，女宝: 6.1-11.6kg</p>
              </div>
            </div>
          </div>

          <div class="milestone-card">
            <h3 class="text-xl font-bold text-[#A57C4F] mb-4">📏 正常身长范围</h3>
            <div class="space-y-3 text-sm">
              <div class="p-3 bg-yellow-50 rounded-lg">
                <h4 class="font-bold text-yellow-800">出生 - 3个月</h4>
                <p class="text-yellow-700">身长每月增长 3-4cm</p>
              </div>
              <div class="p-3 bg-orange-50 rounded-lg">
                <h4 class="font-bold text-orange-800">4-6个月</h4>
                <p class="text-orange-700">身长每月增长 2-2.5cm</p>
              </div>
              <div class="p-3 bg-red-50 rounded-lg">
                <h4 class="font-bold text-red-800">7-12个月</h4>
                <p class="text-red-700">身长每月增长 1.5-2cm</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 大运动发育里程碑 -->
      <section id="motor_development" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">大运动发育里程碑</h2>

        <div class="space-y-8">
          <!-- 0-3个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">0-3个月：基础控制</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">👶</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">头部控制</h4>
                <p class="text-gray-700">俯卧时能抬头45-90度，头部不再摇摆</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">👐</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">原始反射</h4>
                <p class="text-gray-700">握持反射、惊跳反射等原始反射逐渐消失</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🤱</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">肌张力</h4>
                <p class="text-gray-700">四肢肌张力逐渐增强，不再完全松软</p>
              </div>
            </div>
          </div>

          <!-- 4-6个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">4-6个月：翻身与坐立</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🔄</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">翻身</h4>
                <p class="text-gray-700">能从仰卧翻到俯卧，或从俯卧翻到仰卧</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🪑</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">辅助坐立</h4>
                <p class="text-gray-700">在支撑下能坐几分钟，头部稳定</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🤲</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">双手协调</h4>
                <p class="text-gray-700">能用双手抓握并传递物品</p>
              </div>
            </div>
          </div>

          <!-- 7-9个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">7-9个月：坐与爬</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🧘</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">独立坐立</h4>
                <p class="text-gray-700">能独立坐稳，并能转身拿取玩具</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🐛</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">爬行</h4>
                <p class="text-gray-700">开始匍匐前进，进而发展为标准爬行</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🤏</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">精细抓握</h4>
                <p class="text-gray-700">开始尝试用拇指和食指捏取小物品</p>
              </div>
            </div>
          </div>

          <!-- 10-12个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">10-12个月：站立与行走</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🧍</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">扶站</h4>
                <p class="text-gray-700">能扶着家具站起来并保持平衡</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🚶</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">巡航</h4>
                <p class="text-gray-700">扶着家具横向移动几步</p>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">👶</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">独立行走</h4>
                <p class="text-gray-700">可能迈出人生第一步，或至少能独立站立</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 语言和感官 -->
      <section id="language_sensory" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">语言和感官发育</h2>

        <div class="space-y-8">
          <!-- 0-3个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">0-3个月：声音与视觉觉醒</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">听觉发育</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">对突然的声音有惊跳反应</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">喜欢听妈妈的声音</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始发出'啊'、'哦'等元音</span>
                  </li>
                </ul>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">视觉发育</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">只能看清20-30cm距离</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">喜欢黑白对比强烈的图案</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始追视移动的物体</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 4-6个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">4-6个月：咿呀学语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">语言发育</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">发出'ba'、'ma'、'da'等辅音</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始模仿大人的声音</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">对自己的名字有反应</span>
                  </li>
                </ul>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">感官发育</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">用嘴巴探索物品</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">手眼协调能力增强</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始区分熟悉和陌生的声音</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 7-9个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">7-9个月：理解与表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">语言理解</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">理解'不'的含义</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">听懂简单指令如'拍拍手'</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始理解常用词汇</span>
                  </li>
                </ul>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">非语言交流</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">会挥手表示'再见'</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">用手势表达需求</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">模仿大人的动作</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 10-12个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">10-12个月：第一个词汇</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">语言表达</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">可能说出第一个有意义的词</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">词汇量达到1-3个词</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始使用手势配合发声</span>
                  </li>
                </ul>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-3">社交沟通</h4>
                <ul class="space-y-2">
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">能执行简单的指令</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">开始使用点头和摇头</span>
                  </li>
                  <li class="flex items-start">
                    <span class="mr-2 text-green-500 mt-1">✨</span>
                    <span class="text-gray-700">享受与大人的对话游戏</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 认知和思维 -->
      <section id="cognitive_development" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">认知和思维发育</h2>

        <div class="space-y-8">
          <!-- 0-3个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">0-3个月：基础认知</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">注意力</h4>
                <p class="text-gray-700 mb-2">能专注看人脸2-3分钟</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>盯着妈妈的脸看
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">记忆</h4>
                <p class="text-gray-700 mb-2">开始记住重复的声音和活动</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>听到奶瓶声音就兴奋
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">因果关系</h4>
                <p class="text-gray-700 mb-2">还没有因果概念</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>偶然踢到铃铛不会重复
                </div>
              </div>
            </div>
          </div>

          <!-- 4-6个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">4-6个月：探索与发现</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">因果关系</h4>
                <p class="text-gray-700 mb-2">开始理解行动和结果</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>故意踢玩具听声音
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">物体恒存</h4>
                <p class="text-gray-700 mb-2">还未建立完全概念</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>玩具被遮住就忘记
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">分类</h4>
                <p class="text-gray-700 mb-2">开始区分熟悉和陌生</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>看到陌生人会愣住
                </div>
              </div>
            </div>
          </div>

          <!-- 7-9个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">7-9个月：客体永久性</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">物体恒存</h4>
                <p class="text-gray-700 mb-2">知道被遮住的物体依然存在</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>会掀开布找玩具
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">分离焦虑</h4>
                <p class="text-gray-700 mb-2">理解妈妈离开但会回来</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>妈妈离开时哭闹
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">模仿学习</h4>
                <p class="text-gray-700 mb-2">开始有意识地模仿动作</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>学大人拍手
                </div>
              </div>
            </div>
          </div>

          <!-- 10-12个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">10-12个月：解决问题</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">解决问题</h4>
                <p class="text-gray-700 mb-2">能想办法获得想要的东西</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>推开障碍物拿玩具
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">工具使用</h4>
                <p class="text-gray-700 mb-2">开始理解物品的功能</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>用勺子敲击
                </div>
              </div>
              <div class="milestone-card">
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">社会认知</h4>
                <p class="text-gray-700 mb-2">理解简单的社交规则</p>
                <div class="p-2 bg-blue-50 rounded text-sm text-blue-700">
                  <strong>举例：</strong>知道轮流游戏
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 促进宝宝发育的小练习 -->
      <section id="development_activities" class="space-y-6 hidden">
        <h2 class="text-3xl font-bold text-[#A57C4F]">宝宝发育练习</h2>
      

        <div class="space-y-8">
          <!-- 0-3个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">0-3个月：感官刺激</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🤱</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">趴趴时光</h4>
                <p class="text-gray-700 mb-2">每天多次让宝宝趴着，锻炼颈部肌肉</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每次2-5分钟
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">👁️</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">视觉追踪</h4>
                <p class="text-gray-700 mb-2">用彩色玩具在宝宝眼前缓慢移动</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天3-5次
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🎵</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">音乐时光</h4>
                <p class="text-gray-700 mb-2">播放柔和音乐或唱摇篮曲</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天15-30分钟
                </div>
              </div>
            </div>
          </div>

          <!-- 4-6个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">4-6个月：互动探索</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🪞</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">镜子游戏</h4>
                <p class="text-gray-700 mb-2">让宝宝看镜子中的自己</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天10分钟
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">📚</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">亲子阅读</h4>
                <p class="text-gray-700 mb-2">指着图片讲故事给宝宝听</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天10-15分钟
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🎲</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">抓握练习</h4>
                <p class="text-gray-700 mb-2">提供不同材质的安全玩具</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>自由探索
                </div>
              </div>
            </div>
          </div>

          <!-- 7-9个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">7-9个月：运动发展</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🙈</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">躲猫猫</h4>
                <p class="text-gray-700 mb-2">用手或布遮脸再出现</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天5-10次
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🧱</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">积木游戏</h4>
                <p class="text-gray-700 mb-2">给宝宝积木让他敲击和探索</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天20分钟
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🚀</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">爬行空间</h4>
                <p class="text-gray-700 mb-2">创造安全的爬行环境</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天至少1小时
                </div>
              </div>
            </div>
          </div>

          <!-- 10-12个月 -->
          <div>
            <h3 class="text-2xl font-bold text-[#A57C4F] mb-4">10-12个月：技能练习</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="milestone-card">
                <div class="text-3xl mb-3">🥄</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">自主进食</h4>
                <p class="text-gray-700 mb-2">让宝宝尝试用手抓食物</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每餐
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">📦</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">容器游戏</h4>
                <p class="text-gray-700 mb-2">放进去再倒出来的游戏</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天15分钟
                </div>
              </div>
              <div class="milestone-card">
                <div class="text-3xl mb-3">🎶</div>
                <h4 class="text-lg font-bold text-[#A57C4F] mb-2">音乐舞蹈</h4>
                <p class="text-gray-700 mb-2">随音乐一起摇摆和拍手</p>
                <div class="text-sm text-blue-600 font-medium">
                  <strong>建议时长：</strong>每天10-15分钟
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>

  <footer class="text-center py-6 mt-12 bg-amber-50">
    <p class="text-stone-500 text-sm">爱与陪伴，是宝宝成长最好的礼物。</p>
  </footer>

  <script>
    // 导航配置
    const sections = [
      { id: 'physical_growth', title: '身体发育指标', icon: '📏' },
      { id: 'motor_development', title: '大运动发育里程碑', icon: '🏃‍♀️' },
      { id: 'language_sensory', title: '语言和感官', icon: '👂' },
      { id: 'cognitive_development', title: '认知和思维', icon: '🧠' },
      { id: 'development_activities', title: '宝宝发育小练习', icon: '🎯' }
    ];

    // WHO生长数据
    const whoGrowthData = {
      weight: {
        boy: {
          p3: [2.5, 3.4, 4.4, 5.1, 5.6, 6.1, 6.5, 6.8, 7.1, 7.4, 7.6, 7.8, 8.0],
          p50: [3.3, 4.5, 5.6, 6.4, 7.0, 7.5, 7.9, 8.3, 8.6, 8.9, 9.2, 9.4, 9.6],
          p97: [4.4, 5.8, 7.1, 8.1, 8.8, 9.5, 10.1, 10.5, 11.0, 11.4, 11.8, 12.1, 12.4]
        },
        girl: {
          p3: [2.4, 3.2, 4.0, 4.7, 5.2, 5.7, 6.1, 6.4, 6.7, 7.0, 7.2, 7.4, 7.6],
          p50: [3.2, 4.2, 5.1, 5.8, 6.4, 6.9, 7.3, 7.6, 7.9, 8.2, 8.5, 8.7, 8.9],
          p97: [4.2, 5.5, 6.6, 7.5, 8.2, 8.8, 9.3, 9.8, 10.2, 10.6, 11.0, 11.3, 11.6]
        }
      },
      height: {
        boy: {
          p3: [46.1, 50.8, 54.4, 57.1, 59.1, 60.9, 62.4, 63.8, 65.2, 66.4, 67.6, 68.7, 69.8],
          p50: [49.9, 54.7, 58.4, 61.2, 63.3, 65.1, 66.6, 68.0, 69.4, 70.6, 71.8, 73.0, 74.1],
          p97: [53.7, 58.6, 62.4, 65.3, 67.5, 69.4, 70.9, 72.3, 73.7, 74.9, 76.2, 77.4, 78.5]
        },
        girl: {
          p3: [45.4, 49.8, 53.2, 55.8, 57.8, 59.5, 61.1, 62.5, 63.8, 65.1, 66.3, 67.5, 68.6],
          p50: [49.1, 53.7, 57.1, 59.8, 61.8, 63.6, 65.2, 66.7, 68.0, 69.3, 70.5, 71.7, 72.8],
          p97: [52.9, 57.6, 61.1, 63.9, 65.9, 67.7, 69.3, 70.8, 72.2, 73.5, 74.8, 76.1, 77.2]
        }
      }
    };

    const ageLabels = ["出生", "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];



    document.addEventListener('DOMContentLoaded', function () {
      const mainNav = document.getElementById('main-nav');

      // 创建导航
      sections.forEach(section => {
        const navLink = document.createElement('a');
        navLink.href = `#${section.id}`;
        navLink.dataset.target = section.id;
        navLink.className = 'nav-item text-lg text-center md:text-left p-3 md:p-4 rounded-lg hover:bg-[#EACDA4] hover:text-[#434242] transition-colors duration-200 cursor-pointer';
        navLink.innerHTML = `<span class="mr-3">${section.icon}</span><span class="hidden md:inline">${section.title}</span><span class="md:hidden">${section.title}</span>`;
        mainNav.appendChild(navLink);
      });

      // 导航功能
      const navItems = document.querySelectorAll('.nav-item');
      const sectionElements = document.querySelectorAll('main section');

      function activateSection(targetId) {
        sectionElements.forEach(section => {
          section.classList.toggle('hidden', section.id !== targetId);
        });

        navItems.forEach(item => {
          item.classList.toggle('active', item.dataset.target === targetId);
        });

        window.scrollTo(0, 0);
      }

      mainNav.addEventListener('click', (e) => {
        const targetLink = e.target.closest('.nav-item');
        if (targetLink) {
          e.preventDefault();
          const targetId = targetLink.dataset.target;
          activateSection(targetId);
        }
      });

      // 默认激活第一个标签
      activateSection('physical_growth');

      // 初始化生长图表
      let growthChart;
      let currentGender = 'boy';
      let currentMetric = 'weight';

      function createChart() {
        const ctx = document.getElementById('growthChart').getContext('2d');
        const data = whoGrowthData[currentMetric][currentGender];

        const datasets = [
          {
            label: '第97百分位',
            data: data.p97,
            borderColor: 'rgba(251, 146, 60, 0.5)',
            backgroundColor: 'rgba(251, 146, 60, 0.1)',
            borderWidth: 2,
            fill: '+1',
            pointRadius: 0,
            tension: 0.3
          },
          {
            label: '第50百分位',
            data: data.p50,
            borderColor: 'rgba(234, 179, 8, 1)',
            borderWidth: 3,
            pointRadius: 0,
            tension: 0.3,
            fill: false
          },
          {
            label: '第3百分位',
            data: data.p3,
            borderColor: 'rgba(251, 146, 60, 0.5)',
            backgroundColor: 'rgba(251, 146, 60, 0.1)',
            borderWidth: 2,
            fill: false,
            pointRadius: 0,
            tension: 0.3
          }
        ];

        if (growthChart) {
          growthChart.destroy();
        }

        growthChart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: ageLabels,
            datasets: datasets
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              intersect: false,
              mode: 'index'
            },
            plugins: {
              legend: {
                position: 'top',
                labels: { font: { size: 12 } }
              },
              title: {
                display: true,
                text: `${currentGender === 'boy' ? '男宝宝' : '女宝宝'} ${currentMetric === 'weight' ? '体重' : '身长'} 生长曲线`,
                font: { size: 16 }
              },
              tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: 'white',
                bodyColor: 'white',
                borderColor: 'rgba(255, 255, 255, 0.3)',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                padding: 12
              }
            },
            scales: {
              y: {
                beginAtZero: false,
                title: {
                  display: true,
                  text: currentMetric === 'weight' ? '体重 (kg)' : '身长 (cm)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: '月龄'
                }
              }
            },
            elements: {
              point: {
                hitRadius: 15,
                hoverRadius: 8
              },
              line: {
                tension: 0.3
              }
            }
          }
        });
      }

      function updateButtons() {
        document.querySelectorAll('.gender-btn').forEach(btn => 
          btn.style.backgroundColor = btn.id.includes(currentGender) ? '#fcd34d' : '#ffffff'
        );
        document.querySelectorAll('.metric-btn').forEach(btn => 
          btn.style.backgroundColor = btn.id.includes(currentMetric) ? '#fcd34d' : '#ffffff'
        );
      }

      document.getElementById('btn-boy').addEventListener('click', () => { 
        currentGender = 'boy'; createChart(); updateButtons(); 
      });
      document.getElementById('btn-girl').addEventListener('click', () => { 
        currentGender = 'girl'; createChart(); updateButtons(); 
      });
      document.getElementById('btn-weight').addEventListener('click', () => { 
        currentMetric = 'weight'; createChart(); updateButtons(); 
      });
      document.getElementById('btn-height').addEventListener('click', () => { 
        currentMetric = 'height'; createChart(); updateButtons(); 
      });

      createChart();
      updateButtons();
    });
  </script>
</body>

</html>